#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QFile, QTextStream

class StyleManager:
    """样式管理器，用于加载和应用QSS样式"""

    @staticmethod
    def load_style(style_name):
        """加载指定的样式文件

        Args:
            style_name: 样式文件名（不包含路径和扩展名）

        Returns:
            bool: 是否成功加载样式
        """
        # 构建样式文件路径
        style_path = os.path.join("tdpv", "styles", f"{style_name}.qss")

        # 检查文件是否存在
        if not os.path.exists(style_path):
            print(f"样式文件不存在: {style_path}")
            return False

        # 读取样式文件内容
        try:
            style_file = QFile(style_path)
            if style_file.open(QFile.ReadOnly | QFile.Text):
                stream = QTextStream(style_file)
                style_sheet = stream.readAll()
                style_file.close()

                # 应用样式到应用程序
                QApplication.instance().setStyleSheet(style_sheet)
                print(f"已应用样式: {style_name}")
                return True
            else:
                print(f"无法打开样式文件: {style_path}")
                return False
        except Exception as e:
            print(f"加载样式时出错: {str(e)}")
            return False

    @staticmethod
    def get_available_styles():
        """获取可用的样式列表

        Returns:
            list: 可用样式名称列表
        """
        styles = []
        styles_dir = os.path.join("tdpv", "styles")

        # 检查样式目录是否存在
        if not os.path.exists(styles_dir) or not os.path.isdir(styles_dir):
            return styles

        # 遍历样式目录中的QSS文件
        for file in os.listdir(styles_dir):
            if file.endswith(".qss"):
                style_name = os.path.splitext(file)[0]
                styles.append(style_name)

        return styles
