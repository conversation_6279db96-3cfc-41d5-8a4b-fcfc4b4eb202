#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单侧边栏测试

快速验证侧边栏折叠/展开逻辑
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

def test_sidebar_logic():
    """测试侧边栏逻辑"""
    print("🧪 测试侧边栏折叠/展开逻辑")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from tdpv.gui.widgets.sidebar import Sidebar
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建侧边栏
        sidebar = Sidebar()
        print("✅ 侧边栏创建成功")
        
        # 模拟添加插件
        from PySide6.QtWidgets import QLabel
        
        # 添加测试插件1
        icon1 = QLabel("📁")
        panel1 = QLabel("文件管理器面板")
        success1 = sidebar.add_plugin("test_plugin_1", icon1, panel1)
        print(f"添加插件1: {'成功' if success1 else '失败'}")
        
        # 添加测试插件2
        icon2 = QLabel("👋")
        panel2 = QLabel("Hello插件面板")
        success2 = sidebar.add_plugin("test_plugin_2", icon2, panel2)
        print(f"添加插件2: {'成功' if success2 else '失败'}")
        
        print(f"\n📊 初始状态:")
        print(f"   插件数量: {len(sidebar._plugins)}")
        print(f"   当前选中: {sidebar._current_plugin_index}")
        print(f"   是否展开: {sidebar._is_expanded}")
        
        # 测试点击行为
        print(f"\n🖱️ 测试点击行为:")
        
        # 第一次点击插件1
        print(f"\n1️⃣ 点击插件1")
        print(f"   点击前: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        sidebar.on_plugin_button_clicked(0)
        print(f"   点击后: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        
        # 再次点击插件1（应该折叠）
        print(f"\n2️⃣ 再次点击插件1（应该折叠）")
        print(f"   点击前: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        sidebar.on_plugin_button_clicked(0)
        print(f"   点击后: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        
        # 点击插件2（应该展开并切换）
        print(f"\n3️⃣ 点击插件2（应该展开并切换）")
        print(f"   点击前: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        sidebar.on_plugin_button_clicked(1)
        print(f"   点击后: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        
        # 再次点击插件2（应该折叠）
        print(f"\n4️⃣ 再次点击插件2（应该折叠）")
        print(f"   点击前: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        sidebar.on_plugin_button_clicked(1)
        print(f"   点击后: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
        
        # 检查按钮状态
        print(f"\n🔘 按钮状态检查:")
        for i, plugin in enumerate(sidebar._plugins):
            is_checked = plugin['button'].isChecked()
            plugin_id = plugin.get('id', f'plugin_{i}')
            print(f"   {plugin_id}: {'选中' if is_checked else '未选中'}")
        
        print(f"\n✅ 侧边栏逻辑测试完成！")
        
        # 验证预期行为
        expected_expanded = False  # 最后一次点击应该折叠
        expected_selected = -1     # 折叠时应该没有选中
        
        if sidebar._is_expanded == expected_expanded and sidebar._current_plugin_index == expected_selected:
            print("🎉 行为符合预期！")
            return True
        else:
            print("❌ 行为不符合预期")
            print(f"   预期: 展开={expected_expanded}, 选中={expected_selected}")
            print(f"   实际: 展开={sidebar._is_expanded}, 选中={sidebar._current_plugin_index}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_sidebar_logic()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
