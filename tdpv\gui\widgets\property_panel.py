#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QScrollArea, QLabel,
    QFormLayout, QLineEdit, QSpinBox, QComboBox,
    QCheckBox, QGroupBox, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, Slot

class PropertyPanel(QWidget):
    """Property panel for displaying and editing properties of selected items"""
    
    property_changed = Signal(str, object)  # Property name, new value
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Set up the UI
        self.setup_ui()
        
        # Current object being edited
        self.current_object = None
    
    def setup_ui(self):
        """Set up the property panel UI"""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Scroll area for property widgets
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameShape(QScrollArea.NoFrame)
        
        # Container widget for properties
        self.property_container = QWidget()
        self.property_layout = QVBoxLayout(self.property_container)
        self.property_layout.setContentsMargins(10, 10, 10, 10)
        self.property_layout.setSpacing(10)
        self.property_layout.setAlignment(Qt.AlignTop)
        
        # Add a default label
        self.default_label = QLabel("No item selected")
        self.default_label.setAlignment(Qt.AlignCenter)
        self.property_layout.addWidget(self.default_label)
        
        # Set the container as the scroll area widget
        self.scroll_area.setWidget(self.property_container)
        
        # Add scroll area to main layout
        self.main_layout.addWidget(self.scroll_area)
        
        # Apply styles
        self.setStyleSheet("""
            QLabel {
                color: #CCCCCC;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3F3F46;
                border-radius: 4px;
                margin-top: 12px;
                padding-top: 8px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
    
    def clear(self):
        """Clear all properties"""
        # Remove all widgets except the default label
        for i in reversed(range(self.property_layout.count())):
            widget = self.property_layout.itemAt(i).widget()
            if widget != self.default_label:
                widget.setParent(None)
        
        # Show the default label
        self.default_label.setVisible(True)
        self.current_object = None
    
    def set_object(self, obj):
        """Set the object to display properties for"""
        # Clear existing properties
        self.clear()
        
        if obj is None:
            return
        
        # Hide the default label
        self.default_label.setVisible(False)
        
        # Store the current object
        self.current_object = obj
        
        # Get properties from the object
        properties = obj.get_properties() if hasattr(obj, 'get_properties') else {}
        
        # Create property groups
        for group_name, group_props in properties.items():
            self.add_property_group(group_name, group_props)
    
    def add_property_group(self, group_name, properties):
        """Add a group of properties"""
        # Create a group box
        group_box = QGroupBox(group_name)
        group_layout = QFormLayout(group_box)
        group_layout.setContentsMargins(10, 20, 10, 10)
        group_layout.setSpacing(8)
        
        # Add properties to the group
        for prop_name, prop_data in properties.items():
            prop_type = prop_data.get('type', 'string')
            prop_value = prop_data.get('value', '')
            prop_options = prop_data.get('options', [])
            
            # Create the appropriate editor widget based on property type
            editor = self.create_editor(prop_type, prop_value, prop_options, prop_name)
            
            # Add to form layout
            group_layout.addRow(prop_name, editor)
        
        # Add the group to the main layout
        self.property_layout.addWidget(group_box)
    
    def create_editor(self, prop_type, prop_value, prop_options, prop_name):
        """Create an appropriate editor widget based on property type"""
        if prop_type == 'string':
            editor = QLineEdit(str(prop_value))
            editor.textChanged.connect(lambda text, name=prop_name: self.on_property_changed(name, text))
        
        elif prop_type == 'int':
            editor = QSpinBox()
            editor.setRange(-999999, 999999)
            editor.setValue(int(prop_value) if prop_value else 0)
            editor.valueChanged.connect(lambda value, name=prop_name: self.on_property_changed(name, value))
        
        elif prop_type == 'bool':
            editor = QCheckBox()
            editor.setChecked(bool(prop_value))
            editor.stateChanged.connect(lambda state, name=prop_name: self.on_property_changed(name, state == Qt.Checked))
        
        elif prop_type == 'enum':
            editor = QComboBox()
            editor.addItems(prop_options)
            if prop_value in prop_options:
                editor.setCurrentText(prop_value)
            editor.currentTextChanged.connect(lambda text, name=prop_name: self.on_property_changed(name, text))
        
        else:
            # Default to string editor
            editor = QLineEdit(str(prop_value))
            editor.textChanged.connect(lambda text, name=prop_name: self.on_property_changed(name, text))
        
        return editor
    
    def on_property_changed(self, prop_name, value):
        """Handle property value changes"""
        # Update the object if available
        if self.current_object and hasattr(self.current_object, 'set_property'):
            self.current_object.set_property(prop_name, value)
        
        # Emit the property changed signal
        self.property_changed.emit(prop_name, value)
