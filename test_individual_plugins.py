#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
单独测试每个插件

逐个测试插件以确定问题所在
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

def test_plugin_individually(plugin_name):
    """单独测试一个插件"""
    print(f"\n🧪 测试插件: {plugin_name}")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        from tdpv.plugin import PluginManager
        
        # 确保有QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建插件管理器，指定包含该插件的目录
        manager = PluginManager(['tdpv/plugins'])
        
        # 发现插件
        discovered = manager.discover_plugins()
        print(f"发现插件: {discovered}")

        if plugin_name not in discovered:
            print(f"❌ 没有发现目标插件: {plugin_name}")
            print(f"   发现的插件: {discovered}")
            return False

        # 只加载目标插件
        success = manager.load_plugin(plugin_name)
        print(f"加载插件 {plugin_name}: {'成功' if success else '失败'}")

        if not success:
            print(f"❌ 插件加载失败: {plugin_name}")
            return False

        # 激活插件
        success = manager.activate_plugin(plugin_name)
        print(f"激活插件 {plugin_name}: {'成功' if success else '失败'}")

        # 显示插件状态
        status = manager.get_plugin_status(plugin_name)
        if status:
            print(f"📦 {plugin_name}:")
            print(f"   状态: {status['state']}")
            print(f"   名称: {status['name']}")
            print(f"   版本: {status['version']}")
            if status.get('error_message'):
                print(f"   错误: {status['error_message']}")

        # 清理
        manager.shutdown()

        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 单独插件测试")
    print("=" * 50)
    
    plugins_to_test = [
        'hello_plugin',
        'file_manager', 
        'plugin_manager_ui'
    ]
    
    results = {}
    
    for plugin_name in plugins_to_test:
        results[plugin_name] = test_plugin_individually(plugin_name)
    
    print("\n📊 测试结果总结:")
    print("=" * 50)
    
    for plugin_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{plugin_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 所有插件单独测试都通过！")
        print("问题可能在于多插件同时加载时的冲突。")
    else:
        print("\n⚠️ 部分插件测试失败")
        failed_plugins = [name for name, result in results.items() if not result]
        print(f"失败的插件: {failed_plugins}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
