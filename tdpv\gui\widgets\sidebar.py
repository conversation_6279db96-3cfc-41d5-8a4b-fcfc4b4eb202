#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QToolButton,
    QStackedWidget, QDockWidget
)
from PySide6.QtCore import (
    Qt, QSize, Signal, QTimer
)
from PySide6.QtGui import QIcon


class IconButton(QToolButton):
    """Custom icon button for the sidebar.

    This button is used in the sidebar's icon bar to represent and select plugins.
    It has a custom appearance and behavior optimized for the sidebar.
    """

    def __init__(self, icon_path, tooltip, parent=None):
        """Initialize the icon button.

        Args:
            icon_path: Path to the icon image file
            tooltip: Text to display when hovering over the button
            parent: Parent widget
        """
        super().__init__(parent)

        # Set up button properties
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(48, 48))
        self.setToolTip(tooltip)
        self.setCheckable(True)  # Button can be toggled on/off
        self.setFixedSize(48, 48)

        # Apply custom styling
        self.setStyleSheet("""
            QToolButton {
                border: none;
                border-radius: 4px;
                padding: 8px;
                margin: 5px;
                background-color: transparent;
            }

            QToolButton:hover {
                background-color: #E0E0E0;
            }

            QToolButton:checked {
                background-color: #D0D0D0;
                color: #333333;
            }
        """)


class Sidebar(QDockWidget):
    """A collapsible sidebar implemented as a dock widget.

    The sidebar consists of an icon bar on the left and a content area on the right.
    It can be collapsed to show only the icon bar, and expanded to show both.
    It integrates with the plugin system to display plugin content.
    """

    # Signal emitted when a plugin is selected
    plugin_selected = Signal(str)

    # Signal emitted when a plugin with properties is selected
    plugin_with_properties_selected = Signal(object)  # 发送插件对象

    # Signal emitted when a plugin without properties is selected
    plugin_without_properties_selected = Signal()

    def __init__(self, parent=None):
        """Initialize the sidebar.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Configure dock widget properties
        self._configure_dock_widget()

        # Initialize member variables
        self._initialize_properties()

        # Create the content container
        self._create_content_container()

        # Set up the UI components
        self._setup_ui()

        # Set the initial width
        self.reset_width()

    def _configure_dock_widget(self):
        """Configure the dock widget properties."""
        # Set features and allowed areas
        self.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable)
        self.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)

        # Remove border
        self.setStyleSheet("QDockWidget { border: none; }")

        # Remove title bar
        self.setTitleBarWidget(QWidget())

    def _initialize_properties(self):
        """Initialize member variables."""
        # Expansion state
        self._is_expanded = True

        # Width constants
        self._icon_bar_width = 50
        self._content_width = 200
        self._previous_width = 250  # Initial total width (icon bar + content area)

        # Plugin tracking
        self._current_plugin_index = -1
        self._plugins = []

    def _create_content_container(self):
        """Create the content container widget."""
        self.content_widget = QWidget()
        self.setWidget(self.content_widget)

    def _set_initial_width(self):
        """Set the initial width of the sidebar."""
        self.setFixedWidth(self._previous_width)

    def _setup_ui(self):
        """Set up the sidebar UI components."""
        # Configure content widget
        self.content_widget.setContentsMargins(0, 0, 0, 0)

        # Create main layout
        self._create_main_layout()

        # Create icon bar (left side)
        self._create_icon_bar()

        # Create content area (right side)
        self._create_content_area()

        # Add components to main layout
        self.main_layout.addWidget(self.icon_bar)
        self.main_layout.addWidget(self.content_area)

    def _create_main_layout(self):
        """Create the main horizontal layout."""
        self.main_layout = QHBoxLayout(self.content_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.main_layout.setAlignment(Qt.AlignLeft)

    def _create_icon_bar(self):
        """Create the icon bar on the left side."""
        # Create icon bar widget
        self.icon_bar = QWidget()
        self.icon_bar.setFixedWidth(self._icon_bar_width)
        self.icon_bar.setObjectName("iconBar")
        self.icon_bar.setStyleSheet("""
            #iconBar {
                background-color: #F0F0F0;
                border-right: 1px solid #E0E0E0;
            }
        """)

        # Create icon bar layout
        self.icon_layout = QVBoxLayout(self.icon_bar)
        self.icon_layout.setContentsMargins(0, 10, 0, 10)
        self.icon_layout.setSpacing(0)
        self.icon_layout.setAlignment(Qt.AlignTop)

    def _create_content_area(self):
        """Create the content area on the right side."""
        # Create content area widget
        self.content_area = QWidget()
        self.content_area.setObjectName("contentArea")
        self.content_area.setStyleSheet("""
            #contentArea {
                background-color: #F5F5F5;
            }
        """)

        # Create content area layout
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        # Create stacked widget for plugin content
        self.stacked_widget = QStackedWidget()
        self.content_layout.addWidget(self.stacked_widget)

    def update_button_states(self):
        """Update the checked state of all buttons."""
        for i, p in enumerate(self._plugins):
            p['button'].setChecked(i == self._current_plugin_index)

    def toggle_expansion(self):
        """Toggle the sidebar expansion state."""
        if self._is_expanded:
            self.collapse()
        else:
            self.expand()

    def ensure_expanded(self):
        """Ensure the sidebar is expanded."""
        if not self._is_expanded:
            self.expand()

    def expand(self):
        """Expand the sidebar to show both icon bar and content area."""
        # Make content area visible
        self.content_area.setVisible(True)

        self.reset_width()

        # Update expansion state
        self._is_expanded = True

    def collapse(self):
        """Collapse the sidebar to show only the icon bar."""
        if self._is_expanded:
            # Save current width for later restoration
            self._previous_width = self.width()
            print(f"Saving width: {self._previous_width}")

            # Hide content area
            self.content_area.setVisible(False)

            # Set width to icon bar width
            self.setFixedWidth(self._icon_bar_width)

            # Update expansion state
            self._is_expanded = False

    def _remove_fixed_width(self):
        """Remove fixed width constraint to allow resizing."""
        # Clear fixed width, allow drag resizing
        self.setMaximumWidth(16777215)  # QWIDGETSIZE_MAX
        self.setMinimumWidth(self._icon_bar_width * 2)

    def reset_width(self):
        """Reset the sidebar width to its preious value."""

        # Set fixed width
        self.setFixedWidth(self._previous_width)
        print(f"Reset width to {self._previous_width}")

        # Use timer to delay removing fixed width constraint
        QTimer.singleShot(0, self._remove_fixed_width)

        # Set minimum width for content area
        self.content_area.setMinimumWidth(self._icon_bar_width)

