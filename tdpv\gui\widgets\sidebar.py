#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QToolButton,
    QStackedWidget, QDockWidget
)
from PySide6.QtCore import (
    Qt, QSize, Signal, QTimer
)
from PySide6.QtGui import QIcon


class IconButton(QToolButton):
    """Custom icon button for the sidebar.

    This button is used in the sidebar's icon bar to represent and select plugins.
    It has a custom appearance and behavior optimized for the sidebar.
    """

    def __init__(self, icon_path, tooltip, parent=None):
        """Initialize the icon button.

        Args:
            icon_path: Path to the icon image file
            tooltip: Text to display when hovering over the button
            parent: Parent widget
        """
        super().__init__(parent)

        # Set up button properties
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(48, 48))
        self.setToolTip(tooltip)
        self.setCheckable(True)  # Button can be toggled on/off
        self.setFixedSize(48, 48)

        # Apply custom styling
        self.setStyleSheet("""
            QToolButton {
                border: none;
                border-radius: 4px;
                padding: 8px;
                margin: 5px;
                background-color: transparent;
            }

            QToolButton:hover {
                background-color: #E0E0E0;
            }

            QToolButton:checked {
                background-color: #D0D0D0;
                color: #333333;
            }
        """)


class Sidebar(QDockWidget):
    """A collapsible sidebar implemented as a dock widget.

    The sidebar consists of an icon bar on the left and a content area on the right.
    It can be collapsed to show only the icon bar, and expanded to show both.
    It integrates with the plugin system to display plugin content.
    """

    # Signal emitted when a plugin is selected
    plugin_selected = Signal(str)

    # Signal emitted when a plugin with properties is selected
    plugin_with_properties_selected = Signal(object)  # 发送插件对象

    # Signal emitted when a plugin without properties is selected
    plugin_without_properties_selected = Signal()

    def __init__(self, parent=None):
        """Initialize the sidebar.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        # Configure dock widget properties
        self._configure_dock_widget()

        # Initialize member variables
        self._initialize_properties()

        # Create the content container
        self._create_content_container()

        # Set up the UI components
        self._setup_ui()

        # Set the initial width
        self.reset_width()

    def _configure_dock_widget(self):
        """Configure the dock widget properties."""
        # Set features and allowed areas
        self.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable)
        self.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)

        # Remove border
        self.setStyleSheet("QDockWidget { border: none; }")

        # Remove title bar
        self.setTitleBarWidget(QWidget())

    def _initialize_properties(self):
        """Initialize member variables."""
        # Expansion state
        self._is_expanded = True

        # Width constants
        self._icon_bar_width = 50
        self._content_width = 200
        self._previous_width = 250  # Initial total width (icon bar + content area)

        # Plugin tracking
        self._current_plugin_index = -1
        self._plugins = []

    def _create_content_container(self):
        """Create the content container widget."""
        self.content_widget = QWidget()
        self.setWidget(self.content_widget)

    def _set_initial_width(self):
        """Set the initial width of the sidebar."""
        self.setFixedWidth(self._previous_width)

    def _setup_ui(self):
        """Set up the sidebar UI components."""
        # Configure content widget
        self.content_widget.setContentsMargins(0, 0, 0, 0)

        # Create main layout
        self._create_main_layout()

        # Create icon bar (left side)
        self._create_icon_bar()

        # Create content area (right side)
        self._create_content_area()

        # Add components to main layout
        self.main_layout.addWidget(self.icon_bar)
        self.main_layout.addWidget(self.content_area)

    def _create_main_layout(self):
        """Create the main horizontal layout."""
        self.main_layout = QHBoxLayout(self.content_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.main_layout.setAlignment(Qt.AlignLeft)

    def _create_icon_bar(self):
        """Create the icon bar on the left side."""
        # Create icon bar widget
        self.icon_bar = QWidget()
        self.icon_bar.setFixedWidth(self._icon_bar_width)
        self.icon_bar.setObjectName("iconBar")
        self.icon_bar.setStyleSheet("""
            #iconBar {
                background-color: #F0F0F0;
                border-right: 1px solid #E0E0E0;
            }
        """)

        # Create icon bar layout
        self.icon_layout = QVBoxLayout(self.icon_bar)
        self.icon_layout.setContentsMargins(0, 10, 0, 10)
        self.icon_layout.setSpacing(0)
        self.icon_layout.setAlignment(Qt.AlignTop)

    def _create_content_area(self):
        """Create the content area on the right side."""
        # Create content area widget
        self.content_area = QWidget()
        self.content_area.setObjectName("contentArea")
        self.content_area.setStyleSheet("""
            #contentArea {
                background-color: #F5F5F5;
            }
        """)

        # Create content area layout
        self.content_layout = QVBoxLayout(self.content_area)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        # Create stacked widget for plugin content
        self.stacked_widget = QStackedWidget()
        self.content_layout.addWidget(self.stacked_widget)

    def update_button_states(self):
        """Update the checked state of all buttons."""
        for i, p in enumerate(self._plugins):
            p['button'].setChecked(i == self._current_plugin_index)

    def toggle_expansion(self):
        """Toggle the sidebar expansion state."""
        if self._is_expanded:
            self.collapse()
        else:
            self.expand()

    def ensure_expanded(self):
        """Ensure the sidebar is expanded."""
        if not self._is_expanded:
            self.expand()

    def expand(self):
        """Expand the sidebar to show both icon bar and content area."""
        # Make content area visible
        self.content_area.setVisible(True)

        self.reset_width()

        # Update expansion state
        self._is_expanded = True

    def collapse(self):
        """Collapse the sidebar to show only the icon bar."""
        if self._is_expanded:
            # Save current width for later restoration
            self._previous_width = self.width()
            print(f"Saving width: {self._previous_width}")

            # Hide content area
            self.content_area.setVisible(False)

            # Set width to icon bar width
            self.setFixedWidth(self._icon_bar_width)

            # Update expansion state
            self._is_expanded = False

    def _remove_fixed_width(self):
        """Remove fixed width constraint to allow resizing."""
        # Clear fixed width, allow drag resizing
        self.setMaximumWidth(16777215)  # QWIDGETSIZE_MAX
        self.setMinimumWidth(self._icon_bar_width * 2)

    def reset_width(self):
        """Reset the sidebar width to its preious value."""

        # Set fixed width
        self.setFixedWidth(self._previous_width)
        print(f"Reset width to {self._previous_width}")

        # Use timer to delay removing fixed width constraint
        QTimer.singleShot(0, self._remove_fixed_width)

        # Set minimum width for content area
        self.content_area.setMinimumWidth(self._icon_bar_width)

    def set_plugin_manager(self, plugin_manager):
        """设置插件管理器

        Args:
            plugin_manager: 插件管理器实例
        """
        self.plugin_manager = plugin_manager

    def add_plugin(self, plugin_id: str, icon_widget: QWidget,
                  panel_widget: QWidget = None) -> bool:
        """添加插件到侧边栏

        Args:
            plugin_id: 插件ID
            icon_widget: 图标控件
            panel_widget: 面板控件

        Returns:
            bool: 是否添加成功
        """
        try:
            # 检查插件是否已存在
            for plugin in self._plugins:
                if plugin.get('id') == plugin_id:
                    print(f"插件已存在: {plugin_id}")
                    return False

            # 创建图标按钮
            icon_button = IconButton()
            icon_button.setFixedSize(self._icon_bar_width - 10, self._icon_bar_width - 10)

            # 设置图标内容
            if icon_widget:
                # 将图标控件添加到按钮中
                button_layout = QVBoxLayout(icon_button)
                button_layout.setContentsMargins(0, 0, 0, 0)
                button_layout.addWidget(icon_widget)

            # 添加到图标栏
            self.icon_layout.addWidget(icon_button)

            # 添加面板到堆叠控件
            panel_index = -1
            if panel_widget:
                panel_index = self.stacked_widget.addWidget(panel_widget)

            # 创建插件信息
            plugin_info = {
                'id': plugin_id,
                'button': icon_button,
                'panel': panel_widget,
                'panel_index': panel_index,
                'icon_widget': icon_widget
            }

            # 添加到插件列表
            self._plugins.append(plugin_info)

            # 连接按钮点击事件
            button_index = len(self._plugins) - 1
            icon_button.clicked.connect(lambda: self.on_plugin_button_clicked(button_index))

            print(f"插件已添加到侧边栏: {plugin_id}")
            return True

        except Exception as e:
            print(f"添加插件到侧边栏失败: {plugin_id}, 错误: {e}")
            return False

    def remove_plugin(self, plugin_id: str) -> bool:
        """从侧边栏移除插件

        Args:
            plugin_id: 插件ID

        Returns:
            bool: 是否移除成功
        """
        try:
            # 查找插件
            plugin_index = -1
            for i, plugin in enumerate(self._plugins):
                if plugin.get('id') == plugin_id:
                    plugin_index = i
                    break

            if plugin_index == -1:
                print(f"插件不存在: {plugin_id}")
                return False

            plugin_info = self._plugins[plugin_index]

            # 移除按钮
            if plugin_info['button']:
                self.icon_layout.removeWidget(plugin_info['button'])
                plugin_info['button'].deleteLater()

            # 移除面板
            if plugin_info['panel'] and plugin_info['panel_index'] >= 0:
                self.stacked_widget.removeWidget(plugin_info['panel'])
                plugin_info['panel'].deleteLater()

            # 从插件列表移除
            self._plugins.pop(plugin_index)

            # 更新当前插件索引
            if self._current_plugin_index >= plugin_index:
                self._current_plugin_index -= 1
                if self._current_plugin_index < 0 and self._plugins:
                    self._current_plugin_index = 0
                elif not self._plugins:
                    self._current_plugin_index = -1

            # 更新按钮状态
            self.update_button_states()

            print(f"插件已从侧边栏移除: {plugin_id}")
            return True

        except Exception as e:
            print(f"从侧边栏移除插件失败: {plugin_id}, 错误: {e}")
            return False

    def update_plugin_panel(self, plugin_id: str, panel_widget: QWidget) -> bool:
        """更新插件面板

        Args:
            plugin_id: 插件ID
            panel_widget: 新的面板控件

        Returns:
            bool: 是否更新成功
        """
        try:
            # 查找插件
            for plugin_info in self._plugins:
                if plugin_info.get('id') == plugin_id:
                    # 移除旧面板
                    if plugin_info['panel'] and plugin_info['panel_index'] >= 0:
                        self.stacked_widget.removeWidget(plugin_info['panel'])
                        plugin_info['panel'].deleteLater()

                    # 添加新面板
                    panel_index = self.stacked_widget.addWidget(panel_widget)
                    plugin_info['panel'] = panel_widget
                    plugin_info['panel_index'] = panel_index

                    print(f"插件面板已更新: {plugin_id}")
                    return True

            print(f"插件不存在: {plugin_id}")
            return False

        except Exception as e:
            print(f"更新插件面板失败: {plugin_id}, 错误: {e}")
            return False

    def on_plugin_button_clicked(self, plugin_index: int):
        """插件按钮点击事件处理

        Args:
            plugin_index: 插件索引
        """
        if 0 <= plugin_index < len(self._plugins):
            plugin_info = self._plugins[plugin_index]

            # 切换到对应面板
            if plugin_info['panel_index'] >= 0:
                self.stacked_widget.setCurrentIndex(plugin_info['panel_index'])

            # 更新当前插件索引
            self._current_plugin_index = plugin_index

            # 更新按钮状态
            self.update_button_states()

            # 确保侧边栏展开
            self.ensure_expanded()

            # 发射插件选择信号
            plugin_id = plugin_info.get('id', '')
            self.plugin_selected.emit(plugin_id)

            # 检查插件是否有属性
            plugin_obj = self.get_plugin_object(plugin_id)
            if plugin_obj and hasattr(plugin_obj, 'get_properties'):
                self.plugin_with_properties_selected.emit(plugin_obj)
            else:
                self.plugin_without_properties_selected.emit()

    def get_plugin_object(self, plugin_id: str):
        """获取插件对象

        Args:
            plugin_id: 插件ID

        Returns:
            插件对象实例，如果不存在则返回None
        """
        if hasattr(self, 'plugin_manager') and self.plugin_manager:
            plugin_info = self.plugin_manager.get_plugin_status(plugin_id)
            if plugin_info:
                # 从插件管理器获取插件实例
                plugin_data = self.plugin_manager._plugins.get(plugin_id)
                if plugin_data and plugin_data.instance:
                    return plugin_data.instance
        return None

