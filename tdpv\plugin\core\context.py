#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件上下文模块

该模块提供插件运行时的上下文环境，包括API访问、资源管理、事件处理等功能。
"""

import logging
from typing import Any, Callable, Dict, List, Optional, TYPE_CHECKING
from PySide6.QtWidgets import QWidget, QFileDialog

from ..events import get_event_bus, EventPriority

if TYPE_CHECKING:
    from .manager import PluginManager

logger = logging.getLogger(__name__)


class PluginContext:
    """插件上下文
    
    为插件提供运行时环境和API访问，包括：
    - 事件系统访问
    - 命令注册和执行
    - UI扩展接口
    - 资源管理
    - 配置访问
    """
    
    def __init__(self, plugin_id: str, plugin_manager: 'PluginManager'):
        """初始化插件上下文
        
        Args:
            plugin_id: 插件ID
            plugin_manager: 插件管理器实例
        """
        self.plugin_id = plugin_id
        self._plugin_manager = plugin_manager
        self._event_bus = get_event_bus()
        
        # 资源管理
        self._subscriptions: List[str] = []  # 事件订阅ID列表
        self._commands: List[str] = []  # 注册的命令ID列表
        self._ui_components: Dict[str, QWidget] = {}  # UI组件
        self._cleanup_callbacks: List[Callable] = []  # 清理回调
        
        # 获取主窗口引用
        self._main_window = self._get_main_window()
        
        logger.debug(f"创建插件上下文: {plugin_id}")
    
    def _get_main_window(self):
        """获取主窗口实例"""
        try:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if hasattr(widget, 'sidebar') and hasattr(widget, 'property_panel'):
                        return widget
        except Exception as e:
            logger.warning(f"无法获取主窗口: {e}")
        return None
    
    # ==================== 事件系统 ====================
    
    def subscribe(self, event_pattern: str, handler: Callable,
                 priority: EventPriority = EventPriority.NORMAL,
                 is_async: bool = False, is_once: bool = False) -> str:
        """订阅事件
        
        Args:
            event_pattern: 事件模式
            handler: 事件处理函数
            priority: 事件优先级
            is_async: 是否异步处理
            is_once: 是否只执行一次
            
        Returns:
            str: 订阅ID
        """
        subscription_id = self._event_bus.subscribe(
            event_pattern, handler, priority, is_async, is_once
        )
        self._subscriptions.append(subscription_id)
        
        logger.debug(f"插件 {self.plugin_id} 订阅事件: {event_pattern}")
        return subscription_id
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            bool: 是否成功取消订阅
        """
        success = self._event_bus.unsubscribe(subscription_id)
        if success and subscription_id in self._subscriptions:
            self._subscriptions.remove(subscription_id)
        
        return success
    
    def publish(self, event_type: str, data: Any = None,
               priority: EventPriority = EventPriority.NORMAL) -> None:
        """发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            priority: 事件优先级
        """
        self._event_bus.publish(event_type, data, self.plugin_id, priority)
    
    def publish_async(self, event_type: str, data: Any = None,
                     priority: EventPriority = EventPriority.NORMAL) -> None:
        """发布异步事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            priority: 事件优先级
        """
        self._event_bus.publish_async(event_type, data, self.plugin_id, priority)
    
    # ==================== 命令系统 ====================
    
    def register_command(self, command_id: str, handler: Callable,
                        description: str = "", shortcut: str = "") -> bool:
        """注册命令
        
        Args:
            command_id: 命令ID
            handler: 命令处理函数
            description: 命令描述
            shortcut: 快捷键
            
        Returns:
            bool: 是否注册成功
        """
        try:
            # 获取命令系统
            command_system = self._get_command_system()
            if command_system:
                success = command_system.register_command(
                    command_id, handler, description, shortcut, self.plugin_id
                )
                if success:
                    self._commands.append(command_id)
                    logger.debug(f"插件 {self.plugin_id} 注册命令: {command_id}")
                return success
            else:
                logger.warning("命令系统不可用")
                return False
        except Exception as e:
            logger.error(f"注册命令失败: {command_id}, 错误: {e}")
            return False
    
    def unregister_command(self, command_id: str) -> bool:
        """取消注册命令
        
        Args:
            command_id: 命令ID
            
        Returns:
            bool: 是否成功取消注册
        """
        try:
            command_system = self._get_command_system()
            if command_system:
                success = command_system.unregister_command(command_id)
                if success and command_id in self._commands:
                    self._commands.remove(command_id)
                return success
            return False
        except Exception as e:
            logger.error(f"取消注册命令失败: {command_id}, 错误: {e}")
            return False
    
    def execute_command(self, command_id: str, *args, **kwargs) -> Any:
        """执行命令
        
        Args:
            command_id: 命令ID
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 命令执行结果
        """
        try:
            command_system = self._get_command_system()
            if command_system:
                return command_system.execute_command(command_id, *args, **kwargs)
            else:
                logger.warning("命令系统不可用")
                return None
        except Exception as e:
            logger.error(f"执行命令失败: {command_id}, 错误: {e}")
            return None
    
    def _get_command_system(self):
        """获取命令系统实例"""
        # 这里将在后续实现命令系统时完善
        return None
    
    # ==================== UI扩展 ====================
    
    def add_sidebar_icon(self, item_id: str, icon_widget: QWidget) -> bool:
        """添加侧边栏图标
        
        Args:
            item_id: 项目ID
            icon_widget: 图标控件
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if not self._main_window or not hasattr(self._main_window, 'sidebar'):
                logger.warning("主窗口或侧边栏不可用")
                return False
            
            sidebar = self._main_window.sidebar
            success = sidebar.add_plugin(item_id, icon_widget, None)
            
            if success:
                self._ui_components[f"sidebar_icon_{item_id}"] = icon_widget
                logger.debug(f"插件 {self.plugin_id} 添加侧边栏图标: {item_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加侧边栏图标失败: {item_id}, 错误: {e}")
            return False
    
    def add_sidebar_panel(self, item_id: str, panel_widget: QWidget) -> bool:
        """添加侧边栏面板
        
        Args:
            item_id: 项目ID
            panel_widget: 面板控件
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if not self._main_window or not hasattr(self._main_window, 'sidebar'):
                logger.warning("主窗口或侧边栏不可用")
                return False
            
            sidebar = self._main_window.sidebar
            
            # 如果已有图标，更新面板
            if hasattr(sidebar, 'update_plugin_panel'):
                success = sidebar.update_plugin_panel(item_id, panel_widget)
            else:
                # 否则添加新的插件
                icon_widget = self._ui_components.get(f"sidebar_icon_{item_id}")
                success = sidebar.add_plugin(item_id, icon_widget, panel_widget)
            
            if success:
                self._ui_components[f"sidebar_panel_{item_id}"] = panel_widget
                logger.debug(f"插件 {self.plugin_id} 添加侧边栏面板: {item_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"添加侧边栏面板失败: {item_id}, 错误: {e}")
            return False
    
    def remove_sidebar_item(self, item_id: str) -> bool:
        """移除侧边栏项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            if not self._main_window or not hasattr(self._main_window, 'sidebar'):
                return False
            
            sidebar = self._main_window.sidebar
            success = sidebar.remove_plugin(item_id)
            
            if success:
                # 清理UI组件引用
                self._ui_components.pop(f"sidebar_icon_{item_id}", None)
                self._ui_components.pop(f"sidebar_panel_{item_id}", None)
                logger.debug(f"插件 {self.plugin_id} 移除侧边栏项目: {item_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"移除侧边栏项目失败: {item_id}, 错误: {e}")
            return False
    
    def register_property_panel(self, panel_class) -> bool:
        """注册属性面板
        
        Args:
            panel_class: 属性面板类
            
        Returns:
            bool: 是否注册成功
        """
        try:
            # 这里将在后续实现属性面板系统时完善
            logger.debug(f"插件 {self.plugin_id} 注册属性面板: {panel_class}")
            return True
        except Exception as e:
            logger.error(f"注册属性面板失败: 错误: {e}")
            return False
    
    # ==================== 工具方法 ====================
    
    def show_file_dialog(self, title: str = "选择文件", 
                        file_filter: str = "所有文件 (*.*)") -> Optional[str]:
        """显示文件选择对话框
        
        Args:
            title: 对话框标题
            file_filter: 文件过滤器
            
        Returns:
            Optional[str]: 选择的文件路径，如果取消则返回None
        """
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self._main_window, title, "", file_filter
            )
            return file_path if file_path else None
        except Exception as e:
            logger.error(f"显示文件对话框失败: {e}")
            return None
    
    def show_folder_dialog(self, title: str = "选择文件夹") -> Optional[str]:
        """显示文件夹选择对话框
        
        Args:
            title: 对话框标题
            
        Returns:
            Optional[str]: 选择的文件夹路径，如果取消则返回None
        """
        try:
            folder_path = QFileDialog.getExistingDirectory(
                self._main_window, title, ""
            )
            return folder_path if folder_path else None
        except Exception as e:
            logger.error(f"显示文件夹对话框失败: {e}")
            return None
    
    def get_plugin_info(self, plugin_id: str = None) -> Optional[Dict[str, Any]]:
        """获取插件信息
        
        Args:
            plugin_id: 插件ID，如果为None则返回当前插件信息
            
        Returns:
            Optional[Dict[str, Any]]: 插件信息
        """
        target_id = plugin_id or self.plugin_id
        return self._plugin_manager.get_plugin_status(target_id)
    
    def add_cleanup_callback(self, callback: Callable) -> None:
        """添加清理回调
        
        Args:
            callback: 清理回调函数
        """
        self._cleanup_callbacks.append(callback)
    
    def cleanup(self) -> None:
        """清理插件上下文资源"""
        logger.debug(f"清理插件上下文: {self.plugin_id}")
        
        # 取消所有事件订阅
        for subscription_id in self._subscriptions[:]:
            self.unsubscribe(subscription_id)
        
        # 取消所有命令注册
        for command_id in self._commands[:]:
            self.unregister_command(command_id)
        
        # 移除所有UI组件
        ui_items = list(self._ui_components.keys())
        for item_key in ui_items:
            if item_key.startswith("sidebar_icon_"):
                item_id = item_key.replace("sidebar_icon_", "")
                self.remove_sidebar_item(item_id)
        
        # 执行清理回调
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"执行清理回调失败: {e}")
        
        # 清理资源
        self._subscriptions.clear()
        self._commands.clear()
        self._ui_components.clear()
        self._cleanup_callbacks.clear()
        
        logger.debug(f"插件上下文清理完成: {self.plugin_id}")
