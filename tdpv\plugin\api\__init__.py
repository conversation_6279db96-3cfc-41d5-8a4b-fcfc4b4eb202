#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件API模块

该模块提供插件系统的API接口，包括命令系统、侧边栏扩展、视图管理等功能。
"""

from .commands import (
    CommandRegistry, CommandInfo, CommandState,
    get_command_registry, register_command, execute_command, unregister_command
)

from .sidebar import (
    SidebarManager, SidebarItem,
    get_sidebar_manager, add_sidebar_icon, add_sidebar_panel, remove_sidebar_item
)

from .views import (
    ViewManager, ViewInfo, PropertyPanelInfo,
    get_view_manager, register_view, register_property_panel
)

__all__ = [
    # 命令系统
    'CommandRegistry', 'CommandInfo', 'CommandState',
    'get_command_registry', 'register_command', 'execute_command', 'unregister_command',
    
    # 侧边栏
    'SidebarManager', 'SidebarItem',
    'get_sidebar_manager', 'add_sidebar_icon', 'add_sidebar_panel', 'remove_sidebar_item',
    
    # 视图管理
    'ViewManager', 'ViewInfo', 'PropertyPanelInfo',
    'get_view_manager', 'register_view', 'register_property_panel'
]
