#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
命令系统API模块

该模块提供插件系统的命令注册、执行和管理功能。
"""

import logging
import threading
import time
from typing import Any, Callable, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..events import get_event_bus, CommandEvents, create_command_event_data

logger = logging.getLogger(__name__)


class CommandState(Enum):
    """命令状态枚举"""
    REGISTERED = "registered"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    DISABLED = "disabled"


@dataclass
class CommandInfo:
    """命令信息"""
    command_id: str
    handler: Callable
    description: str
    shortcut: str
    plugin_id: str
    state: CommandState = CommandState.REGISTERED
    execution_count: int = 0
    last_execution_time: Optional[float] = None
    last_execution_duration: Optional[float] = None
    last_error: Optional[str] = None


class CommandRegistry:
    """命令注册表"""
    
    def __init__(self):
        self._commands: Dict[str, CommandInfo] = {}
        self._shortcuts: Dict[str, str] = {}  # 快捷键到命令ID的映射
        self._plugin_commands: Dict[str, List[str]] = {}  # 插件到命令列表的映射
        self._lock = threading.RLock()
        self._event_bus = get_event_bus()
    
    def register_command(self, command_id: str, handler: Callable,
                        description: str = "", shortcut: str = "",
                        plugin_id: str = "") -> bool:
        """注册命令
        
        Args:
            command_id: 命令ID
            handler: 命令处理函数
            description: 命令描述
            shortcut: 快捷键
            plugin_id: 插件ID
            
        Returns:
            bool: 是否注册成功
        """
        with self._lock:
            # 检查命令是否已存在
            if command_id in self._commands:
                logger.warning(f"命令已存在: {command_id}")
                return False
            
            # 检查快捷键冲突
            if shortcut and shortcut in self._shortcuts:
                existing_command = self._shortcuts[shortcut]
                logger.warning(f"快捷键冲突: {shortcut}, 已被命令 {existing_command} 使用")
                return False
            
            # 创建命令信息
            command_info = CommandInfo(
                command_id=command_id,
                handler=handler,
                description=description,
                shortcut=shortcut,
                plugin_id=plugin_id
            )
            
            # 注册命令
            self._commands[command_id] = command_info
            
            # 注册快捷键
            if shortcut:
                self._shortcuts[shortcut] = command_id
            
            # 记录插件命令
            if plugin_id:
                if plugin_id not in self._plugin_commands:
                    self._plugin_commands[plugin_id] = []
                self._plugin_commands[plugin_id].append(command_id)
            
            # 发布事件
            event_data = create_command_event_data(
                command_id=command_id,
                command_name=description,
                additional_data={
                    'plugin_id': plugin_id,
                    'shortcut': shortcut
                }
            )
            self._event_bus.publish(CommandEvents.COMMAND_REGISTERED, event_data)
            
            logger.debug(f"注册命令: {command_id} (插件: {plugin_id})")
            return True
    
    def unregister_command(self, command_id: str) -> bool:
        """取消注册命令
        
        Args:
            command_id: 命令ID
            
        Returns:
            bool: 是否成功取消注册
        """
        with self._lock:
            if command_id not in self._commands:
                logger.warning(f"命令不存在: {command_id}")
                return False
            
            command_info = self._commands[command_id]
            
            # 移除命令
            del self._commands[command_id]
            
            # 移除快捷键
            if command_info.shortcut and command_info.shortcut in self._shortcuts:
                del self._shortcuts[command_info.shortcut]
            
            # 移除插件命令记录
            if command_info.plugin_id and command_info.plugin_id in self._plugin_commands:
                plugin_commands = self._plugin_commands[command_info.plugin_id]
                if command_id in plugin_commands:
                    plugin_commands.remove(command_id)
                if not plugin_commands:
                    del self._plugin_commands[command_info.plugin_id]
            
            # 发布事件
            event_data = create_command_event_data(
                command_id=command_id,
                additional_data={'plugin_id': command_info.plugin_id}
            )
            self._event_bus.publish(CommandEvents.COMMAND_UNREGISTERED, event_data)
            
            logger.debug(f"取消注册命令: {command_id}")
            return True
    
    def execute_command(self, command_id: str, *args, **kwargs) -> Any:
        """执行命令
        
        Args:
            command_id: 命令ID
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 命令执行结果
        """
        with self._lock:
            if command_id not in self._commands:
                logger.error(f"命令不存在: {command_id}")
                return None
            
            command_info = self._commands[command_id]
            
            if command_info.state == CommandState.DISABLED:
                logger.warning(f"命令已禁用: {command_id}")
                return None
            
            if command_info.state == CommandState.EXECUTING:
                logger.warning(f"命令正在执行: {command_id}")
                return None
        
        # 发布执行开始事件
        event_data = create_command_event_data(
            command_id=command_id,
            arguments={'args': args, 'kwargs': kwargs}
        )
        self._event_bus.publish(CommandEvents.COMMAND_EXECUTING, event_data)
        
        start_time = time.time()
        result = None
        
        try:
            # 更新状态
            command_info.state = CommandState.EXECUTING
            
            # 执行命令
            result = command_info.handler(*args, **kwargs)
            
            # 更新统计信息
            execution_time = time.time() - start_time
            command_info.state = CommandState.COMPLETED
            command_info.execution_count += 1
            command_info.last_execution_time = start_time
            command_info.last_execution_duration = execution_time
            command_info.last_error = None
            
            # 发布执行完成事件
            success_event_data = create_command_event_data(
                command_id=command_id,
                result=result,
                execution_time=execution_time
            )
            self._event_bus.publish(CommandEvents.COMMAND_EXECUTED, success_event_data)
            
            logger.debug(f"命令执行成功: {command_id} ({execution_time:.3f}s)")
            
        except Exception as e:
            # 更新错误信息
            execution_time = time.time() - start_time
            command_info.state = CommandState.FAILED
            command_info.last_error = str(e)
            command_info.last_execution_time = start_time
            command_info.last_execution_duration = execution_time
            
            # 发布执行失败事件
            error_event_data = create_command_event_data(
                command_id=command_id,
                error_message=str(e),
                execution_time=execution_time
            )
            self._event_bus.publish(CommandEvents.COMMAND_FAILED, error_event_data)
            
            logger.error(f"命令执行失败: {command_id}, 错误: {e}")
            
        finally:
            # 重置状态
            if command_info.state == CommandState.EXECUTING:
                command_info.state = CommandState.REGISTERED
        
        return result
    
    def execute_by_shortcut(self, shortcut: str, *args, **kwargs) -> Any:
        """通过快捷键执行命令
        
        Args:
            shortcut: 快捷键
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 命令执行结果
        """
        with self._lock:
            if shortcut not in self._shortcuts:
                logger.warning(f"快捷键未注册: {shortcut}")
                return None
            
            command_id = self._shortcuts[shortcut]
        
        return self.execute_command(command_id, *args, **kwargs)
    
    def get_command_info(self, command_id: str) -> Optional[Dict[str, Any]]:
        """获取命令信息
        
        Args:
            command_id: 命令ID
            
        Returns:
            Optional[Dict[str, Any]]: 命令信息
        """
        with self._lock:
            if command_id not in self._commands:
                return None
            
            command_info = self._commands[command_id]
            return {
                'command_id': command_id,
                'description': command_info.description,
                'shortcut': command_info.shortcut,
                'plugin_id': command_info.plugin_id,
                'state': command_info.state.value,
                'execution_count': command_info.execution_count,
                'last_execution_time': command_info.last_execution_time,
                'last_execution_duration': command_info.last_execution_duration,
                'last_error': command_info.last_error
            }
    
    def get_all_commands(self) -> Dict[str, Dict[str, Any]]:
        """获取所有命令信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有命令信息
        """
        result = {}
        with self._lock:
            for command_id in self._commands:
                command_info = self.get_command_info(command_id)
                if command_info:
                    result[command_id] = command_info
        return result
    
    def get_plugin_commands(self, plugin_id: str) -> List[str]:
        """获取插件的所有命令
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            List[str]: 命令ID列表
        """
        with self._lock:
            return self._plugin_commands.get(plugin_id, []).copy()
    
    def unregister_plugin_commands(self, plugin_id: str) -> int:
        """取消注册插件的所有命令
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            int: 取消注册的命令数量
        """
        commands = self.get_plugin_commands(plugin_id)
        count = 0
        
        for command_id in commands:
            if self.unregister_command(command_id):
                count += 1
        
        logger.debug(f"取消注册插件命令: {plugin_id}, 数量: {count}")
        return count
    
    def enable_command(self, command_id: str) -> bool:
        """启用命令
        
        Args:
            command_id: 命令ID
            
        Returns:
            bool: 是否成功启用
        """
        with self._lock:
            if command_id not in self._commands:
                return False
            
            command_info = self._commands[command_id]
            if command_info.state == CommandState.DISABLED:
                command_info.state = CommandState.REGISTERED
                logger.debug(f"启用命令: {command_id}")
                return True
            
            return False
    
    def disable_command(self, command_id: str) -> bool:
        """禁用命令
        
        Args:
            command_id: 命令ID
            
        Returns:
            bool: 是否成功禁用
        """
        with self._lock:
            if command_id not in self._commands:
                return False
            
            command_info = self._commands[command_id]
            if command_info.state != CommandState.DISABLED:
                command_info.state = CommandState.DISABLED
                logger.debug(f"禁用命令: {command_id}")
                return True
            
            return False
    
    def get_shortcuts(self) -> Dict[str, str]:
        """获取所有快捷键映射
        
        Returns:
            Dict[str, str]: 快捷键到命令ID的映射
        """
        with self._lock:
            return self._shortcuts.copy()
    
    def clear(self) -> None:
        """清空所有命令"""
        with self._lock:
            self._commands.clear()
            self._shortcuts.clear()
            self._plugin_commands.clear()
        logger.debug("清空所有命令")


# 全局命令注册表实例
_global_command_registry: Optional[CommandRegistry] = None


def get_command_registry() -> CommandRegistry:
    """获取全局命令注册表实例
    
    Returns:
        CommandRegistry: 全局命令注册表实例
    """
    global _global_command_registry
    if _global_command_registry is None:
        _global_command_registry = CommandRegistry()
    return _global_command_registry


def register_command(command_id: str, handler: Callable,
                    description: str = "", shortcut: str = "",
                    plugin_id: str = "") -> bool:
    """注册命令（便捷函数）
    
    Args:
        command_id: 命令ID
        handler: 命令处理函数
        description: 命令描述
        shortcut: 快捷键
        plugin_id: 插件ID
        
    Returns:
        bool: 是否注册成功
    """
    return get_command_registry().register_command(
        command_id, handler, description, shortcut, plugin_id
    )


def execute_command(command_id: str, *args, **kwargs) -> Any:
    """执行命令（便捷函数）
    
    Args:
        command_id: 命令ID
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        Any: 命令执行结果
    """
    return get_command_registry().execute_command(command_id, *args, **kwargs)


def unregister_command(command_id: str) -> bool:
    """取消注册命令（便捷函数）
    
    Args:
        command_id: 命令ID
        
    Returns:
        bool: 是否成功取消注册
    """
    return get_command_registry().unregister_command(command_id)
