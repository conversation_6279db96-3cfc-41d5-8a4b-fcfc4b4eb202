#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件加载器模块

该模块提供插件的动态加载、模块管理和热重载功能。
"""

import importlib
import importlib.util
import logging
import os
import sys
import threading
import time
from pathlib import Path
from typing import Any, Dict, Optional, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)


class PluginModuleCache:
    """插件模块缓存"""
    
    def __init__(self):
        self._modules: Dict[str, Any] = {}
        self._module_paths: Dict[str, str] = {}
        self._lock = threading.RLock()
    
    def get_module(self, plugin_id: str) -> Optional[Any]:
        """获取缓存的模块
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            Optional[Any]: 模块对象，如果不存在则返回None
        """
        with self._lock:
            return self._modules.get(plugin_id)
    
    def set_module(self, plugin_id: str, module: Any, module_path: str) -> None:
        """设置模块缓存
        
        Args:
            plugin_id: 插件ID
            module: 模块对象
            module_path: 模块路径
        """
        with self._lock:
            self._modules[plugin_id] = module
            self._module_paths[plugin_id] = module_path
    
    def remove_module(self, plugin_id: str) -> bool:
        """移除模块缓存
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            removed = False
            if plugin_id in self._modules:
                del self._modules[plugin_id]
                removed = True
            if plugin_id in self._module_paths:
                del self._module_paths[plugin_id]
                removed = True
            return removed
    
    def get_module_path(self, plugin_id: str) -> Optional[str]:
        """获取模块路径
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            Optional[str]: 模块路径
        """
        with self._lock:
            return self._module_paths.get(plugin_id)
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._modules.clear()
            self._module_paths.clear()


class PluginFileWatcher(FileSystemEventHandler):
    """插件文件监控器"""
    
    def __init__(self, loader: 'PluginLoader'):
        self.loader = loader
        self._last_modified: Dict[str, float] = {}
        self._debounce_time = 1.0  # 防抖时间（秒）
    
    def on_modified(self, event):
        """文件修改事件处理"""
        if event.is_directory:
            return
        
        file_path = event.src_path
        if not file_path.endswith('.py'):
            return
        
        # 防抖处理
        current_time = time.time()
        last_time = self._last_modified.get(file_path, 0)
        if current_time - last_time < self._debounce_time:
            return
        
        self._last_modified[file_path] = current_time
        
        # 查找对应的插件
        plugin_id = self.loader.find_plugin_by_file(file_path)
        if plugin_id:
            logger.info(f"检测到插件文件变更: {file_path}, 插件: {plugin_id}")
            self.loader.schedule_reload(plugin_id)


class PluginLoader:
    """插件加载器
    
    负责插件的动态加载、模块管理和热重载功能：
    - 动态导入插件模块
    - 模块缓存管理
    - 文件监控和热重载
    - 依赖关系处理
    """
    
    def __init__(self, enable_hot_reload: bool = True):
        """初始化插件加载器
        
        Args:
            enable_hot_reload: 是否启用热重载
        """
        self._module_cache = PluginModuleCache()
        self._enable_hot_reload = enable_hot_reload
        self._file_observer: Optional[Observer] = None
        self._watched_paths: Set[str] = set()
        self._reload_queue: Set[str] = set()
        self._reload_lock = threading.Lock()
        
        # 插件路径到插件ID的映射
        self._path_to_plugin: Dict[str, str] = {}
        
        if enable_hot_reload:
            self._setup_file_watcher()
    
    def _setup_file_watcher(self) -> None:
        """设置文件监控器"""
        try:
            self._file_observer = Observer()
            self._file_watcher = PluginFileWatcher(self)
            logger.debug("文件监控器已设置")
        except Exception as e:
            logger.error(f"设置文件监控器失败: {e}")
            self._enable_hot_reload = False
    
    def load_plugin_module(self, plugin_id: str, plugin_path: Path, 
                          main_file: str) -> Optional[Any]:
        """加载插件模块
        
        Args:
            plugin_id: 插件ID
            plugin_path: 插件路径
            main_file: 主文件名
            
        Returns:
            Optional[Any]: 加载的模块对象
        """
        # 检查缓存
        cached_module = self._module_cache.get_module(plugin_id)
        if cached_module:
            logger.debug(f"从缓存加载插件模块: {plugin_id}")
            return cached_module
        
        try:
            # 构建模块路径
            main_file_path = plugin_path / main_file
            if not main_file_path.exists():
                logger.error(f"插件主文件不存在: {main_file_path}")
                return None
            
            # 添加插件路径到sys.path
            plugin_path_str = str(plugin_path)
            if plugin_path_str not in sys.path:
                sys.path.insert(0, plugin_path_str)
            
            # 生成模块名
            module_name = f"plugin_{plugin_id}_{int(time.time())}"
            
            # 使用importlib动态加载模块
            spec = importlib.util.spec_from_file_location(module_name, main_file_path)
            if not spec or not spec.loader:
                logger.error(f"无法创建模块规范: {main_file_path}")
                return None
            
            module = importlib.util.module_from_spec(spec)
            
            # 执行模块
            spec.loader.exec_module(module)
            
            # 缓存模块
            self._module_cache.set_module(plugin_id, module, str(main_file_path))
            
            # 记录路径映射
            self._path_to_plugin[str(main_file_path)] = plugin_id
            
            # 添加文件监控
            if self._enable_hot_reload:
                self._add_file_watch(plugin_path_str)
            
            logger.info(f"成功加载插件模块: {plugin_id}")
            return module
            
        except Exception as e:
            logger.error(f"加载插件模块失败: {plugin_id}, 错误: {e}")
            return None
    
    def unload_plugin_module(self, plugin_id: str) -> bool:
        """卸载插件模块
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            bool: 是否成功卸载
        """
        try:
            # 从缓存中移除
            module_path = self._module_cache.get_module_path(plugin_id)
            removed = self._module_cache.remove_module(plugin_id)
            
            # 移除路径映射
            if module_path and module_path in self._path_to_plugin:
                del self._path_to_plugin[module_path]
            
            # 从sys.modules中移除相关模块
            modules_to_remove = []
            for module_name in sys.modules:
                if plugin_id in module_name:
                    modules_to_remove.append(module_name)
            
            for module_name in modules_to_remove:
                del sys.modules[module_name]
            
            if removed:
                logger.debug(f"成功卸载插件模块: {plugin_id}")
            
            return removed
            
        except Exception as e:
            logger.error(f"卸载插件模块失败: {plugin_id}, 错误: {e}")
            return False
    
    def reload_plugin_module(self, plugin_id: str, plugin_path: Path,
                           main_file: str) -> Optional[Any]:
        """重载插件模块
        
        Args:
            plugin_id: 插件ID
            plugin_path: 插件路径
            main_file: 主文件名
            
        Returns:
            Optional[Any]: 重载的模块对象
        """
        logger.info(f"重载插件模块: {plugin_id}")
        
        # 先卸载
        self.unload_plugin_module(plugin_id)
        
        # 重新加载
        return self.load_plugin_module(plugin_id, plugin_path, main_file)
    
    def _add_file_watch(self, path: str) -> None:
        """添加文件监控
        
        Args:
            path: 要监控的路径
        """
        if not self._enable_hot_reload or not self._file_observer:
            return
        
        if path not in self._watched_paths:
            try:
                self._file_observer.schedule(self._file_watcher, path, recursive=True)
                self._watched_paths.add(path)
                
                # 启动观察器（如果还未启动）
                if not self._file_observer.is_alive():
                    self._file_observer.start()
                
                logger.debug(f"添加文件监控: {path}")
            except Exception as e:
                logger.error(f"添加文件监控失败: {path}, 错误: {e}")
    
    def find_plugin_by_file(self, file_path: str) -> Optional[str]:
        """根据文件路径查找插件ID
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 插件ID
        """
        # 直接匹配
        if file_path in self._path_to_plugin:
            return self._path_to_plugin[file_path]
        
        # 模糊匹配（查找包含该文件的插件目录）
        file_path = os.path.normpath(file_path)
        for module_path, plugin_id in self._path_to_plugin.items():
            module_dir = os.path.dirname(module_path)
            if file_path.startswith(module_dir):
                return plugin_id
        
        return None
    
    def schedule_reload(self, plugin_id: str) -> None:
        """调度插件重载
        
        Args:
            plugin_id: 插件ID
        """
        with self._reload_lock:
            self._reload_queue.add(plugin_id)
        
        # 这里可以添加延迟重载逻辑，避免频繁重载
        # 实际的重载操作应该由插件管理器处理
    
    def get_pending_reloads(self) -> Set[str]:
        """获取待重载的插件列表
        
        Returns:
            Set[str]: 待重载的插件ID集合
        """
        with self._reload_lock:
            pending = self._reload_queue.copy()
            self._reload_queue.clear()
            return pending
    
    def get_loaded_modules(self) -> Dict[str, str]:
        """获取已加载的模块信息
        
        Returns:
            Dict[str, str]: 插件ID到模块路径的映射
        """
        result = {}
        for plugin_id in self._module_cache._modules:
            module_path = self._module_cache.get_module_path(plugin_id)
            if module_path:
                result[plugin_id] = module_path
        return result
    
    def cleanup(self) -> None:
        """清理加载器资源"""
        logger.info("正在清理插件加载器...")
        
        # 停止文件监控
        if self._file_observer and self._file_observer.is_alive():
            self._file_observer.stop()
            self._file_observer.join()
        
        # 清理缓存
        self._module_cache.clear()
        self._path_to_plugin.clear()
        self._watched_paths.clear()
        
        with self._reload_lock:
            self._reload_queue.clear()
        
        logger.info("插件加载器清理完成")
    
    def enable_hot_reload(self, enable: bool = True) -> None:
        """启用或禁用热重载
        
        Args:
            enable: 是否启用热重载
        """
        if enable and not self._enable_hot_reload:
            self._enable_hot_reload = True
            self._setup_file_watcher()
            logger.info("热重载已启用")
        elif not enable and self._enable_hot_reload:
            self._enable_hot_reload = False
            if self._file_observer and self._file_observer.is_alive():
                self._file_observer.stop()
                self._file_observer.join()
            logger.info("热重载已禁用")
    
    def is_hot_reload_enabled(self) -> bool:
        """检查热重载是否启用
        
        Returns:
            bool: 是否启用热重载
        """
        return self._enable_hot_reload
