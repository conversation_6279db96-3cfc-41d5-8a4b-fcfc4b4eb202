#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
视图扩展API模块

该模块提供插件系统的视图扩展接口，允许插件注册自定义视图和属性面板。
"""

import logging
from typing import Any, Dict, List, Optional, Type, Callable
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import QObject, Signal

from ..events import get_event_bus, UIEvents, create_ui_event_data

logger = logging.getLogger(__name__)


class ViewInfo:
    """视图信息"""
    
    def __init__(self, view_id: str, view_class: Type[QWidget], 
                 title: str = "", description: str = "", plugin_id: str = ""):
        self.view_id = view_id
        self.view_class = view_class
        self.title = title
        self.description = description
        self.plugin_id = plugin_id
        self.instances: List[QWidget] = []
        self.properties = {}
    
    def create_instance(self, *args, **kwargs) -> QWidget:
        """创建视图实例"""
        instance = self.view_class(*args, **kwargs)
        self.instances.append(instance)
        return instance
    
    def remove_instance(self, instance: QWidget) -> bool:
        """移除视图实例"""
        if instance in self.instances:
            self.instances.remove(instance)
            return True
        return False
    
    def get_instance_count(self) -> int:
        """获取实例数量"""
        return len(self.instances)


class PropertyPanelInfo:
    """属性面板信息"""
    
    def __init__(self, panel_id: str, panel_class: Type[QWidget],
                 target_types: List[str] = None, plugin_id: str = ""):
        self.panel_id = panel_id
        self.panel_class = panel_class
        self.target_types = target_types or []  # 支持的目标类型
        self.plugin_id = plugin_id
        self.priority = 0  # 优先级，数值越大优先级越高


class ViewManager(QObject):
    """视图管理器"""
    
    # 信号
    view_registered = Signal(str)  # 视图注册信号
    view_unregistered = Signal(str)  # 视图取消注册信号
    view_created = Signal(str, object)  # 视图创建信号
    property_panel_registered = Signal(str)  # 属性面板注册信号
    
    def __init__(self):
        super().__init__()
        self._views: Dict[str, ViewInfo] = {}
        self._property_panels: Dict[str, PropertyPanelInfo] = {}
        self._plugin_views: Dict[str, List[str]] = {}  # 插件到视图的映射
        self._plugin_panels: Dict[str, List[str]] = {}  # 插件到属性面板的映射
        self._event_bus = get_event_bus()
    
    def register_view(self, view_id: str, view_class: Type[QWidget],
                     title: str = "", description: str = "", plugin_id: str = "") -> bool:
        """注册视图
        
        Args:
            view_id: 视图ID
            view_class: 视图类
            title: 视图标题
            description: 视图描述
            plugin_id: 插件ID
            
        Returns:
            bool: 是否注册成功
        """
        if view_id in self._views:
            logger.warning(f"视图已存在: {view_id}")
            return False
        
        try:
            # 验证视图类
            if not issubclass(view_class, QWidget):
                logger.error(f"视图类必须继承自QWidget: {view_class}")
                return False
            
            # 创建视图信息
            view_info = ViewInfo(view_id, view_class, title, description, plugin_id)
            self._views[view_id] = view_info
            
            # 记录插件视图
            if plugin_id:
                if plugin_id not in self._plugin_views:
                    self._plugin_views[plugin_id] = []
                self._plugin_views[plugin_id].append(view_id)
            
            # 发布事件
            event_data = create_ui_event_data(
                widget_id=view_id,
                widget_type='view',
                additional_data={
                    'title': title,
                    'description': description,
                    'plugin_id': plugin_id
                }
            )
            self._event_bus.publish(UIEvents.TAB_CREATED, event_data)
            
            # 发射信号
            self.view_registered.emit(view_id)
            
            logger.debug(f"注册视图: {view_id} (插件: {plugin_id})")
            return True
            
        except Exception as e:
            logger.error(f"注册视图失败: {view_id}, 错误: {e}")
            return False
    
    def unregister_view(self, view_id: str) -> bool:
        """取消注册视图
        
        Args:
            view_id: 视图ID
            
        Returns:
            bool: 是否成功取消注册
        """
        if view_id not in self._views:
            logger.warning(f"视图不存在: {view_id}")
            return False
        
        try:
            view_info = self._views[view_id]
            
            # 销毁所有实例
            for instance in view_info.instances[:]:
                instance.close()
                instance.deleteLater()
            view_info.instances.clear()
            
            # 移除视图
            del self._views[view_id]
            
            # 移除插件视图记录
            if view_info.plugin_id and view_info.plugin_id in self._plugin_views:
                plugin_views = self._plugin_views[view_info.plugin_id]
                if view_id in plugin_views:
                    plugin_views.remove(view_id)
                if not plugin_views:
                    del self._plugin_views[view_info.plugin_id]
            
            # 发射信号
            self.view_unregistered.emit(view_id)
            
            logger.debug(f"取消注册视图: {view_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消注册视图失败: {view_id}, 错误: {e}")
            return False
    
    def create_view(self, view_id: str, *args, **kwargs) -> Optional[QWidget]:
        """创建视图实例
        
        Args:
            view_id: 视图ID
            *args: 构造参数
            **kwargs: 构造关键字参数
            
        Returns:
            Optional[QWidget]: 视图实例，如果创建失败则返回None
        """
        if view_id not in self._views:
            logger.error(f"视图不存在: {view_id}")
            return None
        
        try:
            view_info = self._views[view_id]
            instance = view_info.create_instance(*args, **kwargs)
            
            # 发射信号
            self.view_created.emit(view_id, instance)
            
            logger.debug(f"创建视图实例: {view_id}")
            return instance
            
        except Exception as e:
            logger.error(f"创建视图实例失败: {view_id}, 错误: {e}")
            return None
    
    def register_property_panel(self, panel_id: str, panel_class: Type[QWidget],
                               target_types: List[str] = None, plugin_id: str = "",
                               priority: int = 0) -> bool:
        """注册属性面板
        
        Args:
            panel_id: 面板ID
            panel_class: 面板类
            target_types: 支持的目标类型列表
            plugin_id: 插件ID
            priority: 优先级
            
        Returns:
            bool: 是否注册成功
        """
        if panel_id in self._property_panels:
            logger.warning(f"属性面板已存在: {panel_id}")
            return False
        
        try:
            # 验证面板类
            if not issubclass(panel_class, QWidget):
                logger.error(f"属性面板类必须继承自QWidget: {panel_class}")
                return False
            
            # 创建属性面板信息
            panel_info = PropertyPanelInfo(panel_id, panel_class, target_types, plugin_id)
            panel_info.priority = priority
            self._property_panels[panel_id] = panel_info
            
            # 记录插件属性面板
            if plugin_id:
                if plugin_id not in self._plugin_panels:
                    self._plugin_panels[plugin_id] = []
                self._plugin_panels[plugin_id].append(panel_id)
            
            # 发射信号
            self.property_panel_registered.emit(panel_id)
            
            logger.debug(f"注册属性面板: {panel_id} (插件: {plugin_id})")
            return True
            
        except Exception as e:
            logger.error(f"注册属性面板失败: {panel_id}, 错误: {e}")
            return False
    
    def unregister_property_panel(self, panel_id: str) -> bool:
        """取消注册属性面板
        
        Args:
            panel_id: 面板ID
            
        Returns:
            bool: 是否成功取消注册
        """
        if panel_id not in self._property_panels:
            logger.warning(f"属性面板不存在: {panel_id}")
            return False
        
        try:
            panel_info = self._property_panels[panel_id]
            
            # 移除属性面板
            del self._property_panels[panel_id]
            
            # 移除插件属性面板记录
            if panel_info.plugin_id and panel_info.plugin_id in self._plugin_panels:
                plugin_panels = self._plugin_panels[panel_info.plugin_id]
                if panel_id in plugin_panels:
                    plugin_panels.remove(panel_id)
                if not plugin_panels:
                    del self._plugin_panels[panel_info.plugin_id]
            
            logger.debug(f"取消注册属性面板: {panel_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消注册属性面板失败: {panel_id}, 错误: {e}")
            return False
    
    def get_property_panels_for_type(self, target_type: str) -> List[PropertyPanelInfo]:
        """获取支持指定类型的属性面板
        
        Args:
            target_type: 目标类型
            
        Returns:
            List[PropertyPanelInfo]: 支持的属性面板列表（按优先级排序）
        """
        panels = []
        
        for panel_info in self._property_panels.values():
            if not panel_info.target_types or target_type in panel_info.target_types:
                panels.append(panel_info)
        
        # 按优先级排序
        panels.sort(key=lambda x: x.priority, reverse=True)
        return panels
    
    def get_view_info(self, view_id: str) -> Optional[ViewInfo]:
        """获取视图信息
        
        Args:
            view_id: 视图ID
            
        Returns:
            Optional[ViewInfo]: 视图信息
        """
        return self._views.get(view_id)
    
    def get_all_views(self) -> Dict[str, ViewInfo]:
        """获取所有视图信息
        
        Returns:
            Dict[str, ViewInfo]: 所有视图信息
        """
        return self._views.copy()
    
    def get_plugin_views(self, plugin_id: str) -> List[str]:
        """获取插件的所有视图
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            List[str]: 视图ID列表
        """
        return self._plugin_views.get(plugin_id, []).copy()
    
    def get_plugin_property_panels(self, plugin_id: str) -> List[str]:
        """获取插件的所有属性面板
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            List[str]: 属性面板ID列表
        """
        return self._plugin_panels.get(plugin_id, []).copy()
    
    def unregister_plugin_views(self, plugin_id: str) -> int:
        """取消注册插件的所有视图
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            int: 取消注册的视图数量
        """
        views = self.get_plugin_views(plugin_id)
        count = 0
        
        for view_id in views:
            if self.unregister_view(view_id):
                count += 1
        
        logger.debug(f"取消注册插件视图: {plugin_id}, 数量: {count}")
        return count
    
    def unregister_plugin_property_panels(self, plugin_id: str) -> int:
        """取消注册插件的所有属性面板
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            int: 取消注册的属性面板数量
        """
        panels = self.get_plugin_property_panels(plugin_id)
        count = 0
        
        for panel_id in panels:
            if self.unregister_property_panel(panel_id):
                count += 1
        
        logger.debug(f"取消注册插件属性面板: {plugin_id}, 数量: {count}")
        return count
    
    def clear(self) -> None:
        """清空所有视图和属性面板"""
        # 清空视图
        view_ids = list(self._views.keys())
        for view_id in view_ids:
            self.unregister_view(view_id)
        
        # 清空属性面板
        panel_ids = list(self._property_panels.keys())
        for panel_id in panel_ids:
            self.unregister_property_panel(panel_id)
        
        self._views.clear()
        self._property_panels.clear()
        self._plugin_views.clear()
        self._plugin_panels.clear()
        
        logger.debug("清空所有视图和属性面板")


# 全局视图管理器实例
_global_view_manager: Optional[ViewManager] = None


def get_view_manager() -> ViewManager:
    """获取全局视图管理器实例
    
    Returns:
        ViewManager: 全局视图管理器实例
    """
    global _global_view_manager
    if _global_view_manager is None:
        _global_view_manager = ViewManager()
    return _global_view_manager


def register_view(view_id: str, view_class: Type[QWidget],
                 title: str = "", description: str = "", plugin_id: str = "") -> bool:
    """注册视图（便捷函数）
    
    Args:
        view_id: 视图ID
        view_class: 视图类
        title: 视图标题
        description: 视图描述
        plugin_id: 插件ID
        
    Returns:
        bool: 是否注册成功
    """
    return get_view_manager().register_view(view_id, view_class, title, description, plugin_id)


def register_property_panel(panel_id: str, panel_class: Type[QWidget],
                           target_types: List[str] = None, plugin_id: str = "",
                           priority: int = 0) -> bool:
    """注册属性面板（便捷函数）
    
    Args:
        panel_id: 面板ID
        panel_class: 面板类
        target_types: 支持的目标类型列表
        plugin_id: 插件ID
        priority: 优先级
        
    Returns:
        bool: 是否注册成功
    """
    return get_view_manager().register_property_panel(
        panel_id, panel_class, target_types, plugin_id, priority
    )
