/* 全局样式 */
QWidget {
    background-color: #F5F5F5;
    color: #333333;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* 主窗口样式 */
QMainWindow {
    background-color: #F5F5F5;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #F0F0F0;
    border-bottom: 1px solid #D0D0D0;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #E0E0E0;
    border-radius: 4px;
}

QMenu {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    padding: 4px;
}

QMenu::item {
    padding: 4px 20px 4px 20px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #E0E0E0;
}

/* 工具栏样式 */
QToolBar {
    background-color: #F0F0F0;
    border-bottom: 1px solid #D0D0D0;
    spacing: 2px;
    padding: 2px;
}

QToolButton {
    background-color: transparent;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #E0E0E0;
}

QToolButton:pressed {
    background-color: #D0D0D0;
}

/* 侧边栏样式 */
#iconBar {
    background-color: #F0F0F0;
    border-right: 1px solid #E0E0E0;
}

#contentArea {
    background-color: #F5F5F5;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #D0D0D0;
    background-color: #FFFFFF;
}

QTabBar::tab {
    background-color: #E8E8E8;
    border: 1px solid #D0D0D0;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
}

QTabBar::tab:hover:!selected {
    background-color: #F0F0F0;
}

/* 属性面板样式 */
QDockWidget {
    titlebar-close-icon: url(tdpv/styles/close.svg);
    titlebar-normal-icon: url(tdpv/styles/undock.svg);
}

QDockWidget::title {
    background-color: #E8E8E8;
    padding-left: 10px;
    padding-top: 4px;
    border-bottom: 1px solid #D0D0D0;
}

/* 按钮样式 */
QPushButton {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    border-radius: 4px;
    padding: 4px 12px;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #F0F0F0;
    border: 1px solid #C0C0C0;
}

QPushButton:pressed {
    background-color: #E0E0E0;
}

/* 输入框样式 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    border-radius: 4px;
    padding: 4px;
    selection-background-color: #C0C0C0;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #A0A0A0;
}

/* 下拉框样式 */
QComboBox {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 20px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 20px;
    border-left: 1px solid #D0D0D0;
}

QComboBox::down-arrow {
    image: url(tdpv/styles/down_arrow.svg);
}

QComboBox QAbstractItemView {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    selection-background-color: #E0E0E0;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #F5F5F5;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #C0C0C0;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #A0A0A0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #F5F5F5;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #C0C0C0;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #A0A0A0;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 树视图和列表视图样式 */
QTreeView, QListView, QTableView {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    alternate-background-color: #F9F9F9;
}

QTreeView::item, QListView::item, QTableView::item {
    padding: 4px;
}

QTreeView::item:selected, QListView::item:selected, QTableView::item:selected {
    background-color: #E0E0E0;
    color: #333333;
}

QTreeView::item:hover, QListView::item:hover, QTableView::item:hover {
    background-color: #F0F0F0;
}

QHeaderView::section {
    background-color: #E8E8E8;
    border: 1px solid #D0D0D0;
    padding: 4px;
}

/* 分组框样式 */
QGroupBox {
    border: 1px solid #D0D0D0;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 8px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0 3px;
    background-color: #F5F5F5;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #F0F0F0;
    border-top: 1px solid #D0D0D0;
}

QStatusBar::item {
    border: none;
}

/* 工具提示样式 */
QToolTip {
    background-color: #FFFFD0;
    border: 1px solid #D0D0D0;
    color: #333333;
    padding: 4px;
}

/* 进度条样式 */
QProgressBar {
    background-color: #FFFFFF;
    border: 1px solid #D0D0D0;
    border-radius: 4px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #4CAF50;
    width: 10px;
    margin: 0.5px;
}

/* 复选框和单选按钮样式 */
QCheckBox, QRadioButton {
    spacing: 8px;
}

QCheckBox::indicator, QRadioButton::indicator {
    width: 16px;
    height: 16px;
}

QCheckBox::indicator:unchecked, QRadioButton::indicator:unchecked {
    border: 1px solid #D0D0D0;
    background-color: #FFFFFF;
}

QCheckBox::indicator:checked {
    border: 1px solid #4CAF50;
    background-color: #4CAF50;
}

QRadioButton::indicator:checked {
    border: 1px solid #4CAF50;
    background-color: #FFFFFF;
}

QCheckBox::indicator:hover, QRadioButton::indicator:hover {
    border: 1px solid #A0A0A0;
}
