# TDPV 插件系统使用指南

## 📖 概述

TDPV 插件系统是一个功能强大、易于使用的插件架构，支持动态加载、热重载、事件通信等特性。本指南将帮助您快速上手插件开发和使用。

## 🚀 快速开始

### 1. 创建第一个插件

使用脚手架工具快速创建插件：

```bash
cd tdpv
python -m plugin.tools.scaffold
```

或者在Python中：

```python
from tdpv.plugin.tools import PluginScaffold

scaffold = PluginScaffold()
scaffold.interactive_create()
```

### 2. 插件基本结构

每个插件包含以下文件：

```
my_plugin/
├── manifest.json      # 插件清单
├── plugin_main.py     # 主插件文件
├── resources/         # 资源文件
└── README.md         # 说明文档
```

### 3. 插件清单 (manifest.json)

```json
{
  "name": "my_plugin",
  "version": "1.0.0",
  "description": "我的第一个插件",
  "main": "plugin_main.py",
  "author": "Your Name",
  "license": "MIT",
  "dependencies": [],
  "min_app_version": "1.0.0",
  "keywords": ["example"]
}
```

### 4. 插件主文件 (plugin_main.py)

```python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

class MyPlugin:
    def __init__(self):
        self.context = None
    
    def activate(self, context):
        """插件激活时调用"""
        self.context = context
        
        # 创建UI
        icon = QLabel("🔌")
        panel = self.create_panel()
        
        # 注册到侧边栏
        context.add_sidebar_icon("my_plugin", icon)
        context.add_sidebar_panel("my_plugin", panel)
        
        # 注册命令
        context.register_command("my_plugin.hello", self.say_hello, "说Hello")
        
        # 订阅事件
        context.subscribe("app.*", self.on_app_event)
    
    def create_panel(self):
        """创建插件面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.addWidget(QLabel("我的插件"))
        return panel
    
    def say_hello(self):
        """Hello命令"""
        print("Hello from my plugin!")
    
    def on_app_event(self, event_type, data):
        """处理应用事件"""
        print(f"收到事件: {event_type}")
    
    def deactivate(self):
        """插件停用时调用"""
        if self.context:
            self.context.cleanup()
```

## 🔧 核心功能

### 事件系统

#### 订阅事件

```python
# 订阅特定事件
context.subscribe("file.opened", self.on_file_opened)

# 订阅事件模式
context.subscribe("file.*", self.on_file_event)

# 订阅所有事件
context.subscribe("*", self.on_any_event)
```

#### 发布事件

```python
# 发布同步事件
context.publish("my_plugin.action", {"data": "value"})

# 发布异步事件
context.publish_async("my_plugin.background", {"task": "processing"})
```

### 命令系统

#### 注册命令

```python
# 基本命令
context.register_command("my_plugin.action", self.do_action, "执行动作")

# 带快捷键的命令
context.register_command("my_plugin.save", self.save_data, "保存数据", "Ctrl+S")
```

#### 执行命令

```python
# 在插件内执行其他命令
context.execute_command("file_manager.open")

# 带参数执行
context.execute_command("my_plugin.process", arg1, arg2, option="value")
```

### UI扩展

#### 侧边栏集成

```python
# 添加图标
icon_widget = QLabel("📊")
context.add_sidebar_icon("my_plugin", icon_widget)

# 添加面板
panel_widget = self.create_panel()
context.add_sidebar_panel("my_plugin", panel_widget)
```

#### 属性面板

```python
def get_properties(self):
    """返回插件属性供属性面板显示"""
    return {
        '基本信息': {
            '名称': {'type': 'string', 'value': '我的插件'},
            '状态': {'type': 'string', 'value': '运行中'}
        },
        '设置': {
            '自动保存': {'type': 'bool', 'value': True},
            '间隔时间': {'type': 'int', 'value': 30}
        }
    }

def set_property(self, prop_name, value):
    """处理属性变更"""
    print(f"属性 {prop_name} 设置为 {value}")
```

### 工具方法

#### 文件对话框

```python
# 选择文件
file_path = context.show_file_dialog("选择文件", "文本文件 (*.txt)")

# 选择文件夹
folder_path = context.show_folder_dialog("选择文件夹")
```

#### 插件信息

```python
# 获取当前插件信息
info = context.get_plugin_info()

# 获取其他插件信息
other_info = context.get_plugin_info("other_plugin")
```

## 📚 高级功能

### 插件依赖

在 manifest.json 中声明依赖：

```json
{
  "dependencies": ["base_plugin", "utility_plugin"]
}
```

### 热重载

插件支持热重载，修改代码后自动重新加载：

```python
# 在开发模式下启用热重载
from tdpv.plugin import initialize_plugin_system

manager = initialize_plugin_system(enable_hot_reload=True)
```

### 错误处理

```python
def activate(self, context):
    try:
        # 插件初始化代码
        self.setup_ui()
        self.register_commands()
    except Exception as e:
        print(f"插件激活失败: {e}")
        # 清理已创建的资源
        self.cleanup_partial_state()
```

### 资源管理

```python
def activate(self, context):
    # 添加清理回调
    context.add_cleanup_callback(self.cleanup_resources)
    
    # 创建资源
    self.timer = QTimer()
    self.timer.start(1000)

def cleanup_resources(self):
    """清理资源"""
    if hasattr(self, 'timer'):
        self.timer.stop()
```

## 🛠️ 开发工具

### 插件脚手架

```python
from tdpv.plugin.tools import PluginScaffold

scaffold = PluginScaffold()

# 创建基础插件
scaffold.create_plugin("my_basic_plugin", "basic", "基础插件", "Author")

# 创建UI插件
scaffold.create_plugin("my_ui_plugin", "ui", "UI插件", "Author")

# 创建命令插件
scaffold.create_plugin("my_cmd_plugin", "command", "命令插件", "Author")
```

### 调试和测试

```python
# 测试插件系统
python test_plugin_system.py

# 运行完整演示
python demo_plugin_system.py
```

## 📋 最佳实践

### 1. 插件设计原则

- **单一职责**：每个插件专注于一个功能领域
- **松耦合**：通过事件和命令与其他插件交互
- **错误隔离**：妥善处理异常，避免影响其他插件
- **资源清理**：在停用时正确清理所有资源

### 2. 性能优化

- **延迟加载**：只在需要时创建UI组件
- **事件节流**：避免频繁发布事件
- **内存管理**：及时释放不需要的对象

### 3. 用户体验

- **一致性**：遵循应用的UI设计规范
- **响应性**：避免阻塞主线程
- **可配置**：提供必要的设置选项

### 4. 代码质量

- **文档化**：为公共方法添加文档字符串
- **类型提示**：使用类型注解提高代码可读性
- **测试覆盖**：编写单元测试验证功能

## 🔍 故障排除

### 常见问题

1. **插件无法加载**
   - 检查 manifest.json 格式
   - 确认主文件路径正确
   - 查看错误日志

2. **UI组件不显示**
   - 确保在QApplication环境中运行
   - 检查侧边栏注册是否成功
   - 验证UI组件创建代码

3. **事件未收到**
   - 确认事件模式匹配
   - 检查订阅是否成功
   - 验证事件发布代码

4. **命令执行失败**
   - 确认命令注册成功
   - 检查命令ID拼写
   - 查看命令执行日志

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看插件状态
manager = get_plugin_manager()
status = manager.get_plugin_status("my_plugin")
print(status)

# 查看事件统计
event_bus = get_event_bus()
stats = event_bus.get_stats()
print(stats)
```

## 📞 支持和反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看设计文档 `PLUGIN_SYSTEM_DESIGN.md`
2. 运行测试脚本验证环境
3. 查看示例插件代码
4. 提交问题报告或功能请求

---

**祝您插件开发愉快！** 🎉
