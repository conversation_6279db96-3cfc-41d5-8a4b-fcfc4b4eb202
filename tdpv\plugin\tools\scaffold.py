#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件脚手架工具

该模块提供插件开发的脚手架工具，帮助开发者快速创建新插件。
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional


class PluginScaffold:
    """插件脚手架工具"""
    
    def __init__(self, base_dir: str = "tdpv/plugins"):
        self.base_dir = Path(base_dir)
        self.templates_dir = Path(__file__).parent / "templates"
        
        # 插件模板
        self.templates = {
            "basic": {
                "name": "基础插件",
                "description": "基础功能插件模板",
                "files": ["plugin_main.py", "manifest.json"]
            },
            "ui": {
                "name": "UI插件", 
                "description": "带用户界面的插件模板",
                "files": ["plugin_main.py", "manifest.json", "ui_components.py"]
            },
            "command": {
                "name": "命令插件",
                "description": "命令行功能插件模板", 
                "files": ["plugin_main.py", "manifest.json", "commands.py"]
            }
        }
    
    def create_plugin(self, plugin_name: str, plugin_type: str = "basic",
                     description: str = "", author: str = "Unknown") -> bool:
        """创建新插件
        
        Args:
            plugin_name: 插件名称
            plugin_type: 插件类型 (basic, ui, command)
            description: 插件描述
            author: 作者
            
        Returns:
            bool: 是否创建成功
        """
        if plugin_type not in self.templates:
            print(f"不支持的插件类型: {plugin_type}")
            return False
        
        # 创建插件目录
        plugin_dir = self.base_dir / plugin_name
        if plugin_dir.exists():
            print(f"插件目录已存在: {plugin_dir}")
            return False
        
        try:
            # 创建目录结构
            plugin_dir.mkdir(parents=True, exist_ok=True)
            resources_dir = plugin_dir / "resources"
            resources_dir.mkdir(exist_ok=True)
            
            # 生成manifest.json
            self._generate_manifest(plugin_dir, plugin_name, description, author)
            
            # 生成主文件
            self._generate_main_file(plugin_dir, plugin_name, plugin_type)
            
            # 生成其他文件
            if plugin_type == "ui":
                self._generate_ui_components(plugin_dir, plugin_name)
            elif plugin_type == "command":
                self._generate_commands_file(plugin_dir, plugin_name)
            
            # 生成README
            self._generate_readme(plugin_dir, plugin_name, description, plugin_type)
            
            print(f"✅ 插件 {plugin_name} 创建成功")
            print(f"路径: {plugin_dir.absolute()}")
            return True
            
        except Exception as e:
            print(f"创建插件失败: {e}")
            # 清理已创建的目录
            if plugin_dir.exists():
                shutil.rmtree(plugin_dir)
            return False
    
    def _generate_manifest(self, plugin_dir: Path, name: str, 
                          description: str, author: str) -> None:
        """生成manifest.json文件"""
        manifest = {
            "name": name,
            "version": "1.0.0",
            "description": description or f"{name}插件",
            "main": "plugin_main.py",
            "author": author,
            "license": "MIT",
            "dependencies": [],
            "min_app_version": "1.0.0",
            "keywords": [name.lower()],
            "homepage": "",
            "repository": ""
        }
        
        manifest_path = plugin_dir / "manifest.json"
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    def _generate_main_file(self, plugin_dir: Path, name: str, plugin_type: str) -> None:
        """生成主插件文件"""
        class_name = f"{name.title().replace('_', '')}Plugin"
        
        if plugin_type == "basic":
            content = self._get_basic_plugin_template(class_name, name)
        elif plugin_type == "ui":
            content = self._get_ui_plugin_template(class_name, name)
        elif plugin_type == "command":
            content = self._get_command_plugin_template(class_name, name)
        else:
            content = self._get_basic_plugin_template(class_name, name)
        
        main_file = plugin_dir / "plugin_main.py"
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _get_basic_plugin_template(self, class_name: str, plugin_name: str) -> str:
        """获取基础插件模板"""
        return f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{plugin_name}插件

基础插件模板，提供基本的插件功能。
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel


class {class_name}:
    """基础插件类"""
    
    def __init__(self):
        self.context = None
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        print(f"{plugin_name}插件已激活")
        
        # 创建简单的UI
        icon_widget = self.create_icon_widget()
        panel_widget = self.create_panel_widget()
        
        # 注册到侧边栏
        context.add_sidebar_icon("{plugin_name}", icon_widget)
        context.add_sidebar_panel("{plugin_name}", panel_widget)
        
        # 注册命令
        context.register_command("{plugin_name}.hello", self.say_hello, "说Hello")
        
        # 订阅事件
        context.subscribe("app.*", self.on_app_event)
    
    def create_icon_widget(self):
        """创建图标控件"""
        icon = QLabel("🔌")
        icon.setToolTip("{plugin_name}插件")
        return icon
    
    def create_panel_widget(self):
        """创建面板控件"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        title = QLabel("{plugin_name}插件")
        title.setStyleSheet("font-weight: bold;")
        layout.addWidget(title)
        
        desc = QLabel("这是一个基础插件模板。")
        layout.addWidget(desc)
        
        return panel
    
    def say_hello(self):
        """Hello命令处理"""
        print(f"Hello from {{plugin_name}} plugin!")
        if self.context:
            self.context.publish("plugin.{plugin_name}.hello", {{"message": "Hello!"}})
    
    def on_app_event(self, event_type, data):
        """应用事件处理"""
        print(f"{plugin_name}插件收到事件: {{event_type}}")
    
    def deactivate(self):
        """停用插件"""
        print(f"{plugin_name}插件已停用")
        if self.context:
            self.context.cleanup()
'''
    
    def _get_ui_plugin_template(self, class_name: str, plugin_name: str) -> str:
        """获取UI插件模板"""
        return f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{plugin_name}插件

UI插件模板，提供丰富的用户界面功能。
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QGroupBox
)
from PySide6.QtCore import Qt


class {class_name}:
    """UI插件类"""
    
    def __init__(self):
        self.context = None
        self.data = []
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        print(f"{plugin_name}插件已激活")
        
        # 创建UI组件
        icon_widget = self.create_icon_widget()
        panel_widget = self.create_panel_widget()
        
        # 注册到侧边栏
        context.add_sidebar_icon("{plugin_name}", icon_widget)
        context.add_sidebar_panel("{plugin_name}", panel_widget)
        
        # 注册命令
        context.register_command("{plugin_name}.show", self.show_data, "显示数据")
        context.register_command("{plugin_name}.clear", self.clear_data, "清空数据")
        
        # 订阅事件
        context.subscribe("*", self.on_any_event)
    
    def create_icon_widget(self):
        """创建图标控件"""
        icon = QLabel("🎨")
        icon.setAlignment(Qt.AlignCenter)
        icon.setToolTip("{plugin_name}插件")
        icon.setFixedSize(32, 32)
        return icon
    
    def create_panel_widget(self):
        """创建面板控件"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title = QLabel("{plugin_name}插件")
        title.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title)
        
        # 控制组
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout(control_group)
        
        show_btn = QPushButton("显示")
        show_btn.clicked.connect(self.show_data)
        control_layout.addWidget(show_btn)
        
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_data)
        control_layout.addWidget(clear_btn)
        
        layout.addWidget(control_group)
        
        # 数据显示区
        data_group = QGroupBox("数据")
        data_layout = QVBoxLayout(data_group)
        
        self.data_text = QTextEdit()
        self.data_text.setMaximumHeight(150)
        data_layout.addWidget(self.data_text)
        
        layout.addWidget(data_group)
        layout.addStretch()
        
        return panel
    
    def show_data(self):
        """显示数据"""
        info = f"插件: {plugin_name}\\n数据项: {{len(self.data)}}\\n状态: 活跃"
        if hasattr(self, 'data_text'):
            self.data_text.append(f"[显示] {{info}}")
        print(f"显示数据: {{info}}")
    
    def clear_data(self):
        """清空数据"""
        self.data.clear()
        if hasattr(self, 'data_text'):
            self.data_text.clear()
        print("数据已清空")
    
    def on_any_event(self, event_type, data):
        """事件处理"""
        self.data.append({{"event": event_type, "data": str(data)}})
        if hasattr(self, 'data_text'):
            self.data_text.append(f"[事件] {{event_type}}")
    
    def get_properties(self):
        """获取属性"""
        return {{
            '插件信息': {{
                '名称': {{'type': 'string', 'value': '{plugin_name}'}},
                '类型': {{'type': 'string', 'value': 'UI插件'}},
                '状态': {{'type': 'string', 'value': '已激活'}}
            }},
            '统计': {{
                '数据项数': {{'type': 'int', 'value': len(self.data)}}
            }}
        }}
    
    def deactivate(self):
        """停用插件"""
        print(f"{plugin_name}插件已停用")
        if self.context:
            self.context.cleanup()
'''
    
    def _get_command_plugin_template(self, class_name: str, plugin_name: str) -> str:
        """获取命令插件模板"""
        return f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{plugin_name}插件

命令插件模板，提供命令行功能。
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel


class {class_name}:
    """命令插件类"""
    
    def __init__(self):
        self.context = None
        self.command_count = 0
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        print(f"{plugin_name}插件已激活")
        
        # 创建UI组件
        icon_widget = self.create_icon_widget()
        panel_widget = self.create_panel_widget()
        
        # 注册到侧边栏
        context.add_sidebar_icon("{plugin_name}", icon_widget)
        context.add_sidebar_panel("{plugin_name}", panel_widget)
        
        # 注册多个命令
        context.register_command("{plugin_name}.execute", self.execute_command, "执行命令", "Ctrl+E")
        context.register_command("{plugin_name}.status", self.show_status, "显示状态")
        context.register_command("{plugin_name}.reset", self.reset_counter, "重置计数器")
    
    def create_icon_widget(self):
        """创建图标控件"""
        icon = QLabel("⚡")
        icon.setToolTip("{plugin_name}插件")
        return icon
    
    def create_panel_widget(self):
        """创建面板控件"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        title = QLabel("{plugin_name}插件")
        title.setStyleSheet("font-weight: bold;")
        layout.addWidget(title)
        
        desc = QLabel("命令插件模板，提供多个命令。")
        layout.addWidget(desc)
        
        self.status_label = QLabel(f"命令执行次数: {{self.command_count}}")
        layout.addWidget(self.status_label)
        
        return panel
    
    def execute_command(self):
        """执行命令"""
        self.command_count += 1
        message = f"命令已执行 {{self.command_count}} 次"
        
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"命令执行次数: {{self.command_count}}")
        
        print(message)
        
        if self.context:
            self.context.publish("{plugin_name}.command_executed", {{
                "count": self.command_count,
                "message": message
            }})
    
    def show_status(self):
        """显示状态"""
        status = f"插件: {plugin_name}\\n执行次数: {{self.command_count}}\\n状态: 正常"
        print(status)
    
    def reset_counter(self):
        """重置计数器"""
        self.command_count = 0
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"命令执行次数: {{self.command_count}}")
        print("计数器已重置")
    
    def deactivate(self):
        """停用插件"""
        print(f"{plugin_name}插件已停用")
        if self.context:
            self.context.cleanup()
'''
    
    def _generate_ui_components(self, plugin_dir: Path, name: str) -> None:
        """生成UI组件文件"""
        content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{name}插件UI组件

该模块包含插件的UI组件定义。
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel


class CustomWidget(QWidget):
    """自定义控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        label = QLabel("自定义控件")
        layout.addWidget(label)
'''
        
        ui_file = plugin_dir / "ui_components.py"
        with open(ui_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_commands_file(self, plugin_dir: Path, name: str) -> None:
        """生成命令文件"""
        content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
{name}插件命令

该模块包含插件的命令定义。
"""


class PluginCommands:
    """插件命令类"""
    
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
    
    def register_commands(self, context):
        """注册所有命令"""
        context.register_command("{name}.cmd1", self.command1, "命令1")
        context.register_command("{name}.cmd2", self.command2, "命令2")
    
    def command1(self):
        """命令1"""
        print("执行命令1")
    
    def command2(self):
        """命令2"""
        print("执行命令2")
'''
        
        cmd_file = plugin_dir / "commands.py"
        with open(cmd_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_readme(self, plugin_dir: Path, name: str, 
                        description: str, plugin_type: str) -> None:
        """生成README文件"""
        content = f'''# {name}插件

{description or f"{name}插件"}

## 插件信息

- **类型**: {self.templates[plugin_type]["name"]}
- **版本**: 1.0.0
- **作者**: Unknown

## 功能特性

- 基础插件功能
- 侧边栏集成
- 命令系统支持
- 事件处理

## 使用方法

1. 将插件放置在 `tdpv/plugins/{name}` 目录下
2. 重启应用程序或使用热重载功能
3. 在侧边栏中找到插件图标
4. 点击图标查看插件面板

## 开发说明

### 文件结构

```
{name}/
├── manifest.json      # 插件清单
├── plugin_main.py     # 主插件文件
├── resources/         # 资源文件
└── README.md         # 说明文档
```

### 主要方法

- `activate(context)`: 插件激活时调用
- `deactivate()`: 插件停用时调用
- `get_properties()`: 获取插件属性（可选）

## 许可证

MIT License
'''
        
        readme_file = plugin_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def list_templates(self) -> None:
        """列出可用的插件模板"""
        print("可用的插件模板:")
        for key, template in self.templates.items():
            print(f"  {key}: {template['name']} - {template['description']}")
    
    def interactive_create(self) -> bool:
        """交互式创建插件"""
        print("🛠️ 插件脚手架工具")
        print("=" * 40)
        
        # 显示模板
        self.list_templates()
        print()
        
        # 收集信息
        plugin_name = input("插件名称: ").strip()
        if not plugin_name:
            print("插件名称不能为空")
            return False
        
        plugin_desc = input("插件描述: ").strip()
        author = input("作者 (可选): ").strip() or "Unknown"
        
        print("\\n选择插件类型:")
        for i, (key, template) in enumerate(self.templates.items(), 1):
            print(f"  {i}. {template['name']}")
        
        try:
            choice = int(input("输入选项 (1-3): "))
            plugin_types = list(self.templates.keys())
            if 1 <= choice <= len(plugin_types):
                plugin_type = plugin_types[choice - 1]
            else:
                print("无效选项，使用默认类型 'basic'")
                plugin_type = "basic"
        except ValueError:
            print("无效输入，使用默认类型 'basic'")
            plugin_type = "basic"
        
        # 创建插件
        print(f"\\n正在创建插件 '{plugin_name}'...")
        return self.create_plugin(plugin_name, plugin_type, plugin_desc, author)


def main():
    """主函数，用于命令行调用"""
    scaffold = PluginScaffold()
    scaffold.interactive_create()


if __name__ == "__main__":
    main()
