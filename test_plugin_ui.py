#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件UI测试脚本

该脚本测试插件是否能正确显示在侧边栏中。
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_plugin_ui():
    """测试插件UI集成"""
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    try:
        # 导入必要的模块
        from tdpv.app.main_window import MainWindow
        from tdpv.plugin import initialize_plugin_system
        from tdpv.app.app_init import load_and_activate_plugins
        from tdpv.manager.style_manager import StyleManager
        
        # 加载样式
        StyleManager.load_style("light_style")
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        print("✅ 主窗口已创建并显示")
        
        # 初始化插件系统
        plugin_manager = initialize_plugin_system(['tdpv/plugins'])
        
        if plugin_manager:
            print("✅ 插件系统初始化成功")
            
            # 设置插件管理器到主窗口
            window.set_plugin_manager(plugin_manager)
            print("✅ 插件管理器已设置到主窗口")
            
            # 延迟加载插件
            def load_plugins():
                print("🔄 开始加载插件...")
                
                # 发现插件
                discovered = plugin_manager.discover_plugins()
                print(f"发现插件: {discovered}")
                
                # 加载插件
                load_success, load_failure = plugin_manager.load_all_plugins()
                print(f"加载结果: 成功 {load_success}, 失败 {load_failure}")
                
                # 激活插件
                activate_success, activate_failure = plugin_manager.activate_all_plugins()
                print(f"激活结果: 成功 {activate_success}, 失败 {activate_failure}")
                
                # 检查侧边栏插件
                if hasattr(window, 'sidebar') and window.sidebar:
                    plugins = window.sidebar._plugins
                    print(f"侧边栏插件数量: {len(plugins)}")
                    for plugin in plugins:
                        print(f"  - {plugin.get('id', 'Unknown')}")
                else:
                    print("❌ 侧边栏不可用")
                
                print("✅ 插件加载完成")
            
            # 延迟500ms加载插件，确保UI完全初始化
            QTimer.singleShot(500, load_plugins)
            
            # 设置窗口关闭时的清理
            def cleanup():
                print("🧹 清理插件系统...")
                if plugin_manager:
                    plugin_manager.shutdown()
                print("✅ 清理完成")
            
            app.aboutToQuit.connect(cleanup)
            
            print("🚀 启动应用程序...")
            print("请检查侧边栏是否显示插件图标")
            print("按 Ctrl+C 或关闭窗口退出")
            
            # 运行应用程序
            return app.exec()
        else:
            print("❌ 插件系统初始化失败")
            return 1
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_plugin_ui())
