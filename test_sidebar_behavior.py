#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试侧边栏折叠/展开行为

验证点击插件图标时的折叠/展开功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

def test_sidebar_behavior():
    """测试侧边栏行为"""
    print("🧪 测试侧边栏折叠/展开行为")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from tdpv.app.main_window import MainWindow
        from tdpv.manager.style_manager import StyleManager
        from tdpv.plugin import initialize_plugin_system
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("TDPV-SidebarTest")
        print("✅ QApplication 创建成功")
        
        # 加载样式
        StyleManager.load_style("light_style")
        print("✅ 样式加载成功")
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        print("✅ 主窗口已显示")
        
        # 初始化插件系统
        plugin_manager = initialize_plugin_system(['tdpv/plugins'])
        if plugin_manager:
            print("✅ 插件系统初始化成功")
            
            # 设置插件管理器到主窗口
            window.set_plugin_manager(plugin_manager)
            print("✅ 插件管理器已设置到主窗口")
            
            # 检查侧边栏
            if hasattr(window, 'sidebar') and window.sidebar:
                sidebar = window.sidebar
                print("✅ 侧边栏可用")
                print(f"   初始状态: {'展开' if sidebar._is_expanded else '折叠'}")
                print(f"   初始宽度: {sidebar.width()}")
                
                # 延迟加载插件
                def load_plugins_and_test():
                    print("\n🔄 开始加载插件...")
                    
                    try:
                        # 发现、加载和激活插件
                        discovered = plugin_manager.discover_plugins()
                        load_success, load_failure = plugin_manager.load_all_plugins()
                        activate_success, activate_failure = plugin_manager.activate_all_plugins()
                        
                        print(f"插件加载: 成功 {load_success}, 失败 {load_failure}")
                        print(f"插件激活: 成功 {activate_success}, 失败 {activate_failure}")
                        
                        # 检查侧边栏插件
                        plugins = sidebar._plugins
                        print(f"\n📊 侧边栏状态:")
                        print(f"   插件数量: {len(plugins)}")
                        print(f"   当前选中: {sidebar._current_plugin_index}")
                        print(f"   是否展开: {sidebar._is_expanded}")
                        
                        for i, plugin in enumerate(plugins):
                            plugin_id = plugin.get('id', 'Unknown')
                            is_checked = plugin['button'].isChecked()
                            print(f"   {i+1}. {plugin_id} - 按钮状态: {'选中' if is_checked else '未选中'}")
                        
                        # 测试点击行为
                        def test_click_behavior():
                            print(f"\n🖱️ 测试点击行为...")
                            
                            if plugins:
                                # 模拟点击第一个插件
                                first_plugin = plugins[0]
                                plugin_id = first_plugin.get('id', 'Unknown')
                                
                                print(f"\n1️⃣ 点击插件: {plugin_id}")
                                print(f"   点击前 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                
                                # 模拟按钮点击
                                sidebar.on_plugin_button_clicked(0)
                                
                                print(f"   点击后 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                print(f"   侧边栏宽度: {sidebar.width()}")
                                
                                # 再次点击同一个插件（应该折叠）
                                def test_second_click():
                                    print(f"\n2️⃣ 再次点击同一插件: {plugin_id}")
                                    print(f"   点击前 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                    
                                    sidebar.on_plugin_button_clicked(0)
                                    
                                    print(f"   点击后 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                    print(f"   侧边栏宽度: {sidebar.width()}")
                                    
                                    # 测试点击另一个插件
                                    if len(plugins) > 1:
                                        def test_different_plugin():
                                            second_plugin = plugins[1]
                                            second_id = second_plugin.get('id', 'Unknown')
                                            
                                            print(f"\n3️⃣ 点击不同插件: {second_id}")
                                            print(f"   点击前 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                            
                                            sidebar.on_plugin_button_clicked(1)
                                            
                                            print(f"   点击后 - 展开: {sidebar._is_expanded}, 选中: {sidebar._current_plugin_index}")
                                            print(f"   侧边栏宽度: {sidebar.width()}")
                                            
                                            print("\n✅ 侧边栏行为测试完成！")
                                            print("请观察侧边栏的折叠/展开行为是否正确。")
                                        
                                        QTimer.singleShot(2000, test_different_plugin)
                                
                                QTimer.singleShot(2000, test_second_click)
                        
                        QTimer.singleShot(1000, test_click_behavior)
                        
                    except Exception as e:
                        print(f"❌ 测试失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 延迟1秒加载插件
                QTimer.singleShot(1000, load_plugins_and_test)
            else:
                print("❌ 侧边栏不可用")
                return 1
        else:
            print("❌ 插件系统初始化失败")
            return 1
        
        # 设置清理函数
        def cleanup():
            print("\n🧹 清理资源...")
            if plugin_manager:
                plugin_manager.shutdown()
            print("✅ 清理完成")
        
        app.aboutToQuit.connect(cleanup)
        
        print("\n🚀 应用程序启动完成")
        print("请观察侧边栏的行为：")
        print("- 点击插件图标应该展开面板")
        print("- 再次点击同一图标应该折叠面板")
        print("- 点击不同图标应该切换到该插件面板")
        print("按 Ctrl+C 或关闭窗口退出")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_sidebar_behavior())
