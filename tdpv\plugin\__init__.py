#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TDPV 插件系统

该模块提供了完整的插件系统实现，包括：
- 插件管理和生命周期控制
- 事件总线和通信机制
- 命令系统和UI扩展
- 热重载和开发工具

使用示例：
    from tdpv.plugin import PluginManager, get_event_bus
    
    # 创建插件管理器
    manager = PluginManager()
    
    # 加载并激活所有插件
    manager.load_all_plugins()
    manager.activate_all_plugins()
    
    # 获取事件总线
    event_bus = get_event_bus()
    event_bus.publish("app.started", None)
"""

# 核心模块
from .core import (
    PluginManager, PluginState, PluginManifest, PluginInfo,
    PluginLoader, PluginContext
)

# 事件系统
from .events import (
    EventBus, EventPriority, get_event_bus, init_event_bus,
    SystemEvents, FileEvents, UIEvents, CommandEvents, DataEvents,
    EventPatterns
)

# API接口
from .api import (
    get_command_registry, register_command, execute_command,
    get_sidebar_manager, add_sidebar_icon, add_sidebar_panel,
    get_view_manager, register_view, register_property_panel
)

__version__ = "1.0.0"
__author__ = "TDPV Team"

__all__ = [
    # 版本信息
    '__version__', '__author__',
    
    # 核心组件
    'PluginManager', 'PluginState', 'PluginManifest', 'PluginInfo',
    'PluginLoader', 'PluginContext',
    
    # 事件系统
    'EventBus', 'EventPriority', 'get_event_bus', 'init_event_bus',
    'SystemEvents', 'FileEvents', 'UIEvents', 'CommandEvents', 'DataEvents',
    'EventPatterns',
    
    # API接口
    'get_command_registry', 'register_command', 'execute_command',
    'get_sidebar_manager', 'add_sidebar_icon', 'add_sidebar_panel',
    'get_view_manager', 'register_view', 'register_property_panel'
]


def initialize_plugin_system(plugin_dirs=None, enable_hot_reload=True):
    """初始化插件系统
    
    Args:
        plugin_dirs: 插件目录列表
        enable_hot_reload: 是否启用热重载
        
    Returns:
        PluginManager: 插件管理器实例
    """
    # 初始化事件总线
    init_event_bus()
    
    # 创建插件管理器
    manager = PluginManager(plugin_dirs)
    
    return manager


def get_plugin_system_info():
    """获取插件系统信息
    
    Returns:
        dict: 插件系统信息
    """
    return {
        'version': __version__,
        'author': __author__,
        'components': {
            'event_bus': 'Event communication system',
            'plugin_manager': 'Plugin lifecycle management',
            'command_system': 'Command registration and execution',
            'ui_extensions': 'Sidebar and view extensions',
            'hot_reload': 'Development hot reload support'
        }
    }
