#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件系统测试脚本

该脚本用于测试插件系统的基本功能。
"""

import sys
import os
import logging

# 添加项目路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_event_bus():
    """测试事件总线"""
    print("🧪 测试事件总线...")
    
    try:
        from tdpv.plugin.events import get_event_bus, EventPriority
        
        event_bus = get_event_bus()
        
        # 测试事件订阅和发布
        received_events = []
        
        def event_handler(event_type, data):
            received_events.append((event_type, data))
            print(f"收到事件: {event_type} - {data}")
        
        # 订阅事件
        subscription_id = event_bus.subscribe("test.*", event_handler)
        print(f"订阅ID: {subscription_id}")
        
        # 发布事件
        event_bus.publish("test.hello", "Hello World!")
        event_bus.publish("test.data", {"key": "value"})
        
        # 检查结果
        assert len(received_events) == 2
        assert received_events[0][0] == "test.hello"
        assert received_events[0][1] == "Hello World!"
        
        print("✅ 事件总线测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 事件总线测试失败: {e}")
        return False


def test_plugin_manager():
    """测试插件管理器"""
    print("🧪 测试插件管理器...")
    
    try:
        from tdpv.plugin.core import PluginManager
        
        # 创建插件管理器
        manager = PluginManager(['tdpv/plugins'])
        
        # 发现插件
        discovered = manager.discover_plugins()
        print(f"发现插件: {discovered}")
        
        # 获取插件状态
        all_plugins = manager.get_all_plugins()
        print(f"所有插件: {list(all_plugins.keys())}")
        
        # 获取统计信息
        stats = manager.get_stats()
        print(f"统计信息: {stats}")
        
        print("✅ 插件管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 插件管理器测试失败: {e}")
        return False


def test_command_system():
    """测试命令系统"""
    print("🧪 测试命令系统...")
    
    try:
        from tdpv.plugin.api import get_command_registry
        
        registry = get_command_registry()
        
        # 注册测试命令
        def test_command(*args, **kwargs):
            return f"测试命令执行: args={args}, kwargs={kwargs}"
        
        success = registry.register_command(
            "test.command", 
            test_command, 
            "测试命令", 
            "Ctrl+T",
            "test_plugin"
        )
        
        assert success, "命令注册失败"
        
        # 执行命令
        result = registry.execute_command("test.command", "arg1", key="value")
        print(f"命令执行结果: {result}")
        
        # 获取命令信息
        info = registry.get_command_info("test.command")
        print(f"命令信息: {info}")
        
        print("✅ 命令系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 命令系统测试失败: {e}")
        return False


def test_plugin_scaffold():
    """测试插件脚手架"""
    print("🧪 测试插件脚手架...")
    
    try:
        from tdpv.plugin.tools import PluginScaffold
        
        scaffold = PluginScaffold("test_plugins")
        
        # 列出模板
        print("可用模板:")
        scaffold.list_templates()
        
        # 创建测试插件
        success = scaffold.create_plugin(
            "test_scaffold_plugin",
            "basic",
            "脚手架测试插件",
            "Test Author"
        )
        
        assert success, "插件创建失败"
        
        # 检查文件是否存在
        import pathlib
        plugin_dir = pathlib.Path("test_plugins/test_scaffold_plugin")
        assert plugin_dir.exists(), "插件目录不存在"
        assert (plugin_dir / "manifest.json").exists(), "manifest.json不存在"
        assert (plugin_dir / "plugin_main.py").exists(), "plugin_main.py不存在"
        
        # 清理测试文件
        import shutil
        shutil.rmtree("test_plugins")
        
        print("✅ 插件脚手架测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 插件脚手架测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始插件系统测试")
    print("=" * 50)
    
    tests = [
        test_event_bus,
        test_plugin_manager,
        test_command_system,
        test_plugin_scaffold
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            failed += 1
        print("-" * 30)
    
    print(f"📊 测试结果: 通过 {passed}, 失败 {failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
