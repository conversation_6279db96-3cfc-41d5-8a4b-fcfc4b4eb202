#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件管理器模块

该模块实现了插件的全生命周期管理，包括加载、激活、停用、重载等功能。
支持依赖解析、状态监控、错误隔离等特性。
"""

import json
import logging
import os
import sys
import threading
import time
import traceback
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field

from ..events import (
    get_event_bus, SystemEvents, PluginEventData, 
    create_plugin_event_data, EventPriority
)

logger = logging.getLogger(__name__)


class PluginState(Enum):
    """插件状态枚举"""
    UNKNOWN = "unknown"
    DISCOVERED = "discovered"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVATING = "activating"
    ACTIVE = "active"
    DEACTIVATING = "deactivating"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginManifest:
    """插件清单"""
    name: str
    version: str
    description: str
    main: str
    author: str = ""
    license: str = ""
    dependencies: List[str] = field(default_factory=list)
    min_app_version: str = ""
    max_app_version: str = ""
    keywords: List[str] = field(default_factory=list)
    homepage: str = ""
    repository: str = ""
    
    @classmethod
    def from_dict(cls, data: dict) -> 'PluginManifest':
        """从字典创建插件清单"""
        return cls(
            name=data.get('name', ''),
            version=data.get('version', '1.0.0'),
            description=data.get('description', ''),
            main=data.get('main', 'plugin_main.py'),
            author=data.get('author', ''),
            license=data.get('license', ''),
            dependencies=data.get('dependencies', []),
            min_app_version=data.get('min_app_version', ''),
            max_app_version=data.get('max_app_version', ''),
            keywords=data.get('keywords', []),
            homepage=data.get('homepage', ''),
            repository=data.get('repository', '')
        )


@dataclass
class PluginInfo:
    """插件信息"""
    plugin_id: str
    manifest: PluginManifest
    path: Path
    state: PluginState = PluginState.DISCOVERED
    instance: Optional[Any] = None
    context: Optional[Any] = None
    load_time: Optional[float] = None
    activate_time: Optional[float] = None
    error_message: Optional[str] = None
    last_modified: Optional[float] = None


class PluginManager:
    """插件管理器
    
    负责插件的全生命周期管理：
    - 插件扫描与发现
    - 依赖解析与拓扑排序
    - 插件加载、激活、停用
    - 状态监控与错误隔离
    - 热重载支持
    """
    
    def __init__(self, plugin_dirs: List[str] = None):
        """初始化插件管理器
        
        Args:
            plugin_dirs: 插件目录列表
        """
        self._plugins: Dict[str, PluginInfo] = {}
        self._plugin_dirs = plugin_dirs or ['tdpv/plugins']
        self._lock = threading.RLock()
        self._event_bus = get_event_bus()
        
        # 状态统计
        self._stats = {
            'total_plugins': 0,
            'active_plugins': 0,
            'failed_plugins': 0,
            'load_time': 0.0
        }
        
        # 依赖图
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._reverse_dependency_graph: Dict[str, Set[str]] = {}
    
    def discover_plugins(self) -> List[str]:
        """发现插件
        
        Returns:
            List[str]: 发现的插件ID列表
        """
        discovered_plugins = []
        
        for plugin_dir in self._plugin_dirs:
            plugin_dir_path = Path(plugin_dir)
            if not plugin_dir_path.exists():
                logger.warning(f"插件目录不存在: {plugin_dir}")
                continue
            
            # 扫描插件目录
            for item in plugin_dir_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    manifest_path = item / 'manifest.json'
                    if manifest_path.exists():
                        try:
                            plugin_id = self._load_plugin_manifest(item)
                            if plugin_id:
                                discovered_plugins.append(plugin_id)
                        except Exception as e:
                            logger.error(f"加载插件清单失败: {item}, 错误: {e}")
        
        logger.info(f"发现 {len(discovered_plugins)} 个插件")
        return discovered_plugins
    
    def _load_plugin_manifest(self, plugin_path: Path) -> Optional[str]:
        """加载插件清单
        
        Args:
            plugin_path: 插件路径
            
        Returns:
            Optional[str]: 插件ID，如果加载失败则返回None
        """
        manifest_path = plugin_path / 'manifest.json'
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_data = json.load(f)
            
            manifest = PluginManifest.from_dict(manifest_data)
            plugin_id = manifest.name
            
            # 检查插件是否已存在
            if plugin_id in self._plugins:
                logger.warning(f"插件ID冲突: {plugin_id}")
                return None
            
            # 创建插件信息
            plugin_info = PluginInfo(
                plugin_id=plugin_id,
                manifest=manifest,
                path=plugin_path,
                state=PluginState.DISCOVERED,
                last_modified=manifest_path.stat().st_mtime
            )
            
            with self._lock:
                self._plugins[plugin_id] = plugin_info
                self._stats['total_plugins'] += 1
            
            logger.debug(f"发现插件: {plugin_id} ({manifest.version})")
            return plugin_id
            
        except Exception as e:
            logger.error(f"加载插件清单失败: {manifest_path}, 错误: {e}")
            return None
    
    def load_plugin(self, plugin_id: str) -> bool:
        """加载插件
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            bool: 是否加载成功
        """
        with self._lock:
            if plugin_id not in self._plugins:
                logger.error(f"插件不存在: {plugin_id}")
                return False
            
            plugin_info = self._plugins[plugin_id]
            
            if plugin_info.state in [PluginState.LOADED, PluginState.ACTIVE]:
                logger.warning(f"插件已加载: {plugin_id}")
                return True
            
            if plugin_info.state == PluginState.ERROR:
                logger.warning(f"插件处于错误状态: {plugin_id}")
                return False
        
        # 发布加载开始事件
        event_data = create_plugin_event_data(
            plugin_id=plugin_id,
            plugin_name=plugin_info.manifest.name,
            plugin_version=plugin_info.manifest.version,
            plugin_path=str(plugin_info.path)
        )
        self._event_bus.publish(SystemEvents.PLUGIN_LOADING, event_data)
        
        try:
            # 更新状态
            plugin_info.state = PluginState.LOADING
            start_time = time.time()
            
            # 加载插件模块
            plugin_module = self._import_plugin_module(plugin_info)
            if not plugin_module:
                raise Exception("无法导入插件模块")
            
            # 创建插件实例
            plugin_instance = self._create_plugin_instance(plugin_module, plugin_info)
            if not plugin_instance:
                raise Exception("无法创建插件实例")
            
            # 更新插件信息
            plugin_info.instance = plugin_instance
            plugin_info.state = PluginState.LOADED
            plugin_info.load_time = time.time() - start_time
            plugin_info.error_message = None
            
            # 发布加载完成事件
            self._event_bus.publish(SystemEvents.PLUGIN_LOADED, event_data)
            
            logger.info(f"插件加载成功: {plugin_id} ({plugin_info.load_time:.3f}s)")
            return True
            
        except Exception as e:
            error_msg = f"加载插件失败: {plugin_id}, 错误: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            
            # 更新错误状态
            plugin_info.state = PluginState.ERROR
            plugin_info.error_message = str(e)
            self._stats['failed_plugins'] += 1
            
            # 发布错误事件
            error_event_data = create_plugin_event_data(
                plugin_id=plugin_id,
                plugin_name=plugin_info.manifest.name,
                plugin_version=plugin_info.manifest.version,
                error_message=str(e)
            )
            self._event_bus.publish(SystemEvents.PLUGIN_ERROR, error_event_data)
            
            return False
    
    def _import_plugin_module(self, plugin_info: PluginInfo):
        """导入插件模块
        
        Args:
            plugin_info: 插件信息
            
        Returns:
            插件模块对象
        """
        main_file = plugin_info.path / plugin_info.manifest.main
        if not main_file.exists():
            raise Exception(f"插件主文件不存在: {main_file}")
        
        # 添加插件路径到sys.path
        plugin_path_str = str(plugin_info.path)
        if plugin_path_str not in sys.path:
            sys.path.insert(0, plugin_path_str)
        
        try:
            # 动态导入模块
            module_name = plugin_info.manifest.main.replace('.py', '')
            spec = __import__(module_name)
            return spec
        except Exception as e:
            raise Exception(f"导入插件模块失败: {e}")
    
    def _create_plugin_instance(self, plugin_module, plugin_info: PluginInfo):
        """创建插件实例
        
        Args:
            plugin_module: 插件模块
            plugin_info: 插件信息
            
        Returns:
            插件实例
        """
        # 查找插件类
        plugin_class = None
        
        # 尝试常见的插件类名模式
        possible_names = [
            f"{plugin_info.manifest.name}Plugin",
            f"{plugin_info.manifest.name.title()}Plugin",
            "Plugin",
            "MainPlugin"
        ]
        
        for name in possible_names:
            if hasattr(plugin_module, name):
                plugin_class = getattr(plugin_module, name)
                break
        
        if not plugin_class:
            # 查找第一个以Plugin结尾的类
            for attr_name in dir(plugin_module):
                attr = getattr(plugin_module, attr_name)
                if (isinstance(attr, type) and 
                    attr_name.endswith('Plugin') and 
                    attr_name != 'Plugin'):
                    plugin_class = attr
                    break
        
        if not plugin_class:
            raise Exception("未找到插件类")
        
        # 创建插件实例
        try:
            return plugin_class()
        except Exception as e:
            raise Exception(f"创建插件实例失败: {e}")
    
    def activate_plugin(self, plugin_id: str) -> bool:
        """激活插件
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            bool: 是否激活成功
        """
        with self._lock:
            if plugin_id not in self._plugins:
                logger.error(f"插件不存在: {plugin_id}")
                return False
            
            plugin_info = self._plugins[plugin_id]
            
            if plugin_info.state == PluginState.ACTIVE:
                logger.warning(f"插件已激活: {plugin_id}")
                return True
            
            if plugin_info.state != PluginState.LOADED:
                logger.error(f"插件未加载，无法激活: {plugin_id}")
                return False
        
        # 检查依赖
        if not self._check_dependencies(plugin_id):
            logger.error(f"插件依赖检查失败: {plugin_id}")
            return False
        
        # 发布激活开始事件
        event_data = create_plugin_event_data(
            plugin_id=plugin_id,
            plugin_name=plugin_info.manifest.name,
            plugin_version=plugin_info.manifest.version
        )
        self._event_bus.publish(SystemEvents.PLUGIN_ACTIVATING, event_data)
        
        try:
            # 更新状态
            plugin_info.state = PluginState.ACTIVATING
            start_time = time.time()
            
            # 创建插件上下文
            from .context import PluginContext
            plugin_context = PluginContext(plugin_id, self)
            plugin_info.context = plugin_context
            
            # 调用插件的activate方法
            if hasattr(plugin_info.instance, 'activate'):
                plugin_info.instance.activate(plugin_context)
            
            # 更新状态
            plugin_info.state = PluginState.ACTIVE
            plugin_info.activate_time = time.time() - start_time
            self._stats['active_plugins'] += 1
            
            # 发布激活完成事件
            self._event_bus.publish(SystemEvents.PLUGIN_ACTIVATED, event_data)
            
            logger.info(f"插件激活成功: {plugin_id} ({plugin_info.activate_time:.3f}s)")
            return True
            
        except Exception as e:
            error_msg = f"激活插件失败: {plugin_id}, 错误: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())
            
            # 更新错误状态
            plugin_info.state = PluginState.ERROR
            plugin_info.error_message = str(e)
            self._stats['failed_plugins'] += 1
            
            # 发布错误事件
            error_event_data = create_plugin_event_data(
                plugin_id=plugin_id,
                plugin_name=plugin_info.manifest.name,
                plugin_version=plugin_info.manifest.version,
                error_message=str(e)
            )
            self._event_bus.publish(SystemEvents.PLUGIN_ERROR, error_event_data)
            
            return False
    
    def _check_dependencies(self, plugin_id: str) -> bool:
        """检查插件依赖
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            bool: 依赖是否满足
        """
        plugin_info = self._plugins[plugin_id]
        
        for dep_id in plugin_info.manifest.dependencies:
            if dep_id not in self._plugins:
                logger.error(f"依赖插件不存在: {dep_id}")
                return False
            
            dep_plugin = self._plugins[dep_id]
            if dep_plugin.state != PluginState.ACTIVE:
                logger.error(f"依赖插件未激活: {dep_id}")
                return False
        
        return True

    def deactivate_plugin(self, plugin_id: str) -> bool:
        """停用插件

        Args:
            plugin_id: 插件ID

        Returns:
            bool: 是否停用成功
        """
        with self._lock:
            if plugin_id not in self._plugins:
                logger.error(f"插件不存在: {plugin_id}")
                return False

            plugin_info = self._plugins[plugin_id]

            if plugin_info.state != PluginState.ACTIVE:
                logger.warning(f"插件未激活: {plugin_id}")
                return True

        # 检查是否有其他插件依赖此插件
        dependents = self._get_dependents(plugin_id)
        if dependents:
            logger.error(f"无法停用插件，其他插件依赖它: {plugin_id}, 依赖者: {dependents}")
            return False

        # 发布停用开始事件
        event_data = create_plugin_event_data(
            plugin_id=plugin_id,
            plugin_name=plugin_info.manifest.name,
            plugin_version=plugin_info.manifest.version
        )
        self._event_bus.publish(SystemEvents.PLUGIN_DEACTIVATING, event_data)

        try:
            # 更新状态
            plugin_info.state = PluginState.DEACTIVATING

            # 调用插件的deactivate方法
            if hasattr(plugin_info.instance, 'deactivate'):
                plugin_info.instance.deactivate()

            # 清理插件上下文
            if plugin_info.context:
                plugin_info.context.cleanup()
                plugin_info.context = None

            # 更新状态
            plugin_info.state = PluginState.INACTIVE
            self._stats['active_plugins'] -= 1

            # 发布停用完成事件
            self._event_bus.publish(SystemEvents.PLUGIN_DEACTIVATED, event_data)

            logger.info(f"插件停用成功: {plugin_id}")
            return True

        except Exception as e:
            error_msg = f"停用插件失败: {plugin_id}, 错误: {e}"
            logger.error(error_msg)
            logger.debug(traceback.format_exc())

            # 更新错误状态
            plugin_info.state = PluginState.ERROR
            plugin_info.error_message = str(e)

            return False

    def _get_dependents(self, plugin_id: str) -> List[str]:
        """获取依赖指定插件的插件列表

        Args:
            plugin_id: 插件ID

        Returns:
            List[str]: 依赖者插件ID列表
        """
        dependents = []

        for pid, plugin_info in self._plugins.items():
            if (plugin_info.state == PluginState.ACTIVE and
                plugin_id in plugin_info.manifest.dependencies):
                dependents.append(pid)

        return dependents

    def reload_plugin(self, plugin_id: str) -> bool:
        """重载插件

        Args:
            plugin_id: 插件ID

        Returns:
            bool: 是否重载成功
        """
        with self._lock:
            if plugin_id not in self._plugins:
                logger.error(f"插件不存在: {plugin_id}")
                return False

            plugin_info = self._plugins[plugin_id]
            was_active = plugin_info.state == PluginState.ACTIVE

        # 如果插件已激活，先停用
        if was_active:
            if not self.deactivate_plugin(plugin_id):
                logger.error(f"重载插件失败，无法停用: {plugin_id}")
                return False

        # 重新加载
        success = self.load_plugin(plugin_id)

        # 如果之前是激活状态，重新激活
        if success and was_active:
            success = self.activate_plugin(plugin_id)

        if success:
            logger.info(f"插件重载成功: {plugin_id}")
        else:
            logger.error(f"插件重载失败: {plugin_id}")

        return success

    def get_plugin_status(self, plugin_id: str) -> Optional[Dict[str, Any]]:
        """获取插件状态信息

        Args:
            plugin_id: 插件ID

        Returns:
            Optional[Dict[str, Any]]: 插件状态信息，如果插件不存在则返回None
        """
        with self._lock:
            if plugin_id not in self._plugins:
                return None

            plugin_info = self._plugins[plugin_id]

            return {
                'plugin_id': plugin_id,
                'name': plugin_info.manifest.name,
                'version': plugin_info.manifest.version,
                'description': plugin_info.manifest.description,
                'author': plugin_info.manifest.author,
                'state': plugin_info.state.value,
                'path': str(plugin_info.path),
                'load_time': plugin_info.load_time,
                'activate_time': plugin_info.activate_time,
                'error_message': plugin_info.error_message,
                'dependencies': plugin_info.manifest.dependencies,
                'last_modified': plugin_info.last_modified
            }

    def get_all_plugins(self) -> Dict[str, Dict[str, Any]]:
        """获取所有插件状态信息

        Returns:
            Dict[str, Dict[str, Any]]: 所有插件状态信息
        """
        result = {}

        with self._lock:
            for plugin_id in self._plugins:
                status = self.get_plugin_status(plugin_id)
                if status:
                    result[plugin_id] = status

        return result

    def get_active_plugins(self) -> List[str]:
        """获取所有激活的插件ID列表

        Returns:
            List[str]: 激活的插件ID列表
        """
        active_plugins = []

        with self._lock:
            for plugin_id, plugin_info in self._plugins.items():
                if plugin_info.state == PluginState.ACTIVE:
                    active_plugins.append(plugin_id)

        return active_plugins

    def get_stats(self) -> Dict[str, Any]:
        """获取插件管理器统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            state_counts = {}
            for state in PluginState:
                state_counts[state.value] = 0

            for plugin_info in self._plugins.values():
                state_counts[plugin_info.state.value] += 1

            return {
                **self._stats,
                'state_counts': state_counts,
                'plugin_dirs': self._plugin_dirs
            }

    def load_all_plugins(self) -> Tuple[int, int]:
        """加载所有发现的插件

        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        start_time = time.time()
        success_count = 0
        failure_count = 0

        # 先发现插件
        discovered = self.discover_plugins()

        # 解析依赖关系并排序
        load_order = self._resolve_dependencies()

        # 按依赖顺序加载插件
        for plugin_id in load_order:
            if self.load_plugin(plugin_id):
                success_count += 1
            else:
                failure_count += 1

        self._stats['load_time'] = time.time() - start_time

        logger.info(f"插件加载完成: 成功 {success_count}, 失败 {failure_count}, "
                   f"耗时 {self._stats['load_time']:.3f}s")

        return success_count, failure_count

    def activate_all_plugins(self) -> Tuple[int, int]:
        """激活所有已加载的插件

        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        success_count = 0
        failure_count = 0

        # 按依赖顺序激活插件
        load_order = self._resolve_dependencies()

        for plugin_id in load_order:
            with self._lock:
                if (plugin_id in self._plugins and
                    self._plugins[plugin_id].state == PluginState.LOADED):
                    if self.activate_plugin(plugin_id):
                        success_count += 1
                    else:
                        failure_count += 1

        logger.info(f"插件激活完成: 成功 {success_count}, 失败 {failure_count}")

        return success_count, failure_count

    def _resolve_dependencies(self) -> List[str]:
        """解析插件依赖关系并返回加载顺序

        Returns:
            List[str]: 按依赖关系排序的插件ID列表
        """
        # 构建依赖图
        self._build_dependency_graph()

        # 拓扑排序
        return self._topological_sort()

    def _build_dependency_graph(self) -> None:
        """构建依赖关系图"""
        self._dependency_graph.clear()
        self._reverse_dependency_graph.clear()

        with self._lock:
            for plugin_id, plugin_info in self._plugins.items():
                self._dependency_graph[plugin_id] = set(plugin_info.manifest.dependencies)

                # 构建反向依赖图
                for dep_id in plugin_info.manifest.dependencies:
                    if dep_id not in self._reverse_dependency_graph:
                        self._reverse_dependency_graph[dep_id] = set()
                    self._reverse_dependency_graph[dep_id].add(plugin_id)

    def _topological_sort(self) -> List[str]:
        """拓扑排序，返回无依赖冲突的加载顺序

        Returns:
            List[str]: 排序后的插件ID列表
        """
        # Kahn算法实现拓扑排序
        in_degree = {}

        # 计算入度
        for plugin_id in self._dependency_graph:
            in_degree[plugin_id] = len(self._dependency_graph[plugin_id])

        # 找到入度为0的节点
        queue = [pid for pid, degree in in_degree.items() if degree == 0]
        result = []

        while queue:
            current = queue.pop(0)
            result.append(current)

            # 更新依赖此插件的其他插件的入度
            if current in self._reverse_dependency_graph:
                for dependent in self._reverse_dependency_graph[current]:
                    in_degree[dependent] -= 1
                    if in_degree[dependent] == 0:
                        queue.append(dependent)

        # 检查是否有循环依赖
        if len(result) != len(self._dependency_graph):
            remaining = set(self._dependency_graph.keys()) - set(result)
            logger.warning(f"检测到循环依赖，跳过插件: {remaining}")

        return result

    def shutdown(self) -> None:
        """关闭插件管理器"""
        logger.info("正在关闭插件管理器...")

        # 按反向依赖顺序停用所有插件
        active_plugins = self.get_active_plugins()
        load_order = self._resolve_dependencies()

        for plugin_id in reversed(load_order):
            if plugin_id in active_plugins:
                self.deactivate_plugin(plugin_id)

        # 清理资源
        with self._lock:
            self._plugins.clear()
            self._dependency_graph.clear()
            self._reverse_dependency_graph.clear()

        logger.info("插件管理器已关闭")
