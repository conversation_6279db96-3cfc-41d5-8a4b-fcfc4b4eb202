#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试版主程序

用于调试插件系统集成
"""

import sys
import os
import logging

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """调试主函数"""
    print("🐛 调试版TDPV启动")
    print("=" * 40)
    
    try:
        # 导入必要模块
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QTimer
        from tdpv.app.main_window import MainWindow
        from tdpv.manager.style_manager import StyleManager
        from tdpv.plugin import initialize_plugin_system
        from tdpv.app.app_init import load_and_activate_plugins
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("TDPV-Debug")
        print("✅ QApplication 创建成功")
        
        # 加载样式
        StyleManager.load_style("light_style")
        print("✅ 样式加载成功")
        
        # 创建主窗口
        window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 显示主窗口
        window.show()
        print("✅ 主窗口已显示")
        
        # 初始化插件系统
        plugin_manager = initialize_plugin_system(['tdpv/plugins'])
        if plugin_manager:
            print("✅ 插件系统初始化成功")
            
            # 设置插件管理器到主窗口
            window.set_plugin_manager(plugin_manager)
            print("✅ 插件管理器已设置到主窗口")
            
            # 检查侧边栏
            if hasattr(window, 'sidebar') and window.sidebar:
                print("✅ 侧边栏可用")
                print(f"   侧边栏类型: {type(window.sidebar)}")
                print(f"   侧边栏插件数量: {len(window.sidebar._plugins)}")
            else:
                print("❌ 侧边栏不可用")
            
            # 延迟加载插件
            def load_plugins():
                print("\n🔄 开始加载插件...")
                
                try:
                    # 发现插件
                    discovered = plugin_manager.discover_plugins()
                    print(f"发现插件: {discovered}")
                    
                    # 加载插件
                    load_success, load_failure = plugin_manager.load_all_plugins()
                    print(f"加载结果: 成功 {load_success}, 失败 {load_failure}")
                    
                    # 激活插件
                    activate_success, activate_failure = plugin_manager.activate_all_plugins()
                    print(f"激活结果: 成功 {activate_success}, 失败 {activate_failure}")
                    
                    # 检查侧边栏插件
                    if hasattr(window, 'sidebar') and window.sidebar:
                        plugins = window.sidebar._plugins
                        print(f"\n📊 侧边栏状态:")
                        print(f"   插件数量: {len(plugins)}")
                        for i, plugin in enumerate(plugins):
                            print(f"   {i+1}. {plugin.get('id', 'Unknown')}")
                            print(f"      按钮: {plugin.get('button', 'None')}")
                            print(f"      面板: {plugin.get('panel', 'None')}")
                    
                    # 显示所有插件状态
                    print(f"\n📦 插件状态:")
                    all_plugins = plugin_manager.get_all_plugins()
                    for plugin_id, info in all_plugins.items():
                        print(f"   {plugin_id}: {info['state']}")
                        if info.get('error_message'):
                            print(f"      错误: {info['error_message']}")
                    
                    print("\n✅ 插件加载完成")
                    print("请检查侧边栏左侧是否显示插件图标")
                    
                except Exception as e:
                    print(f"❌ 插件加载失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 延迟1秒加载插件
            QTimer.singleShot(1000, load_plugins)
        else:
            print("❌ 插件系统初始化失败")
        
        # 设置清理函数
        def cleanup():
            print("\n🧹 清理资源...")
            if plugin_manager:
                plugin_manager.shutdown()
            print("✅ 清理完成")
        
        app.aboutToQuit.connect(cleanup)
        
        print("\n🚀 应用程序启动完成")
        print("按 Ctrl+C 或关闭窗口退出")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
