#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件管理器UI插件

该插件提供插件系统的图形化管理界面，包括：
- 插件列表显示
- 插件状态管理
- 插件加载/卸载
- 插件信息查看
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget, 
    QTableWidgetItem, QPushButton, QTextEdit, QSplitter,
    QGroupBox, QHeaderView, QMessageBox
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont


class PluginManagerUIPlugin:
    """插件管理器UI插件类"""
    
    def __init__(self):
        self.context = None
        self.plugin_manager = None
        self.refresh_timer = None
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        
        # 获取插件管理器
        if hasattr(context, '_plugin_manager'):
            self.plugin_manager = context._plugin_manager
        
        print("插件管理器UI已激活")
        
        # 创建UI组件
        icon_widget = self.create_icon_widget()
        panel_widget = self.create_panel_widget()
        
        # 注册到侧边栏
        context.add_sidebar_icon("plugin_manager_ui", icon_widget)
        context.add_sidebar_panel("plugin_manager_ui", panel_widget)
        
        # 注册命令
        context.register_command("plugin_manager.refresh", self.refresh_plugin_list, "刷新插件列表", "F5")
        context.register_command("plugin_manager.reload", self.reload_selected_plugin, "重载选中插件")
        
        # 订阅插件系统事件
        context.subscribe("plugin.*", self.on_plugin_event)
        
        # 设置定时刷新
        self.setup_auto_refresh()
    
    def create_icon_widget(self):
        """创建侧边栏图标"""
        icon_widget = QLabel("🔧")
        icon_widget.setAlignment(Qt.AlignCenter)
        icon_widget.setToolTip("插件管理器")
        icon_widget.setFixedSize(32, 32)
        icon_widget.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
                font-size: 18px;
            }
            QLabel:hover {
                background-color: rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
        """)
        return icon_widget
    
    def create_panel_widget(self):
        """创建侧边栏面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("插件管理器")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 插件列表组
        list_group = QGroupBox("插件列表")
        list_layout = QVBoxLayout(list_group)
        
        # 插件表格
        self.plugin_table = QTableWidget()
        self.plugin_table.setColumnCount(4)
        self.plugin_table.setHorizontalHeaderLabels(["名称", "版本", "状态", "作者"])
        
        # 设置表格属性
        header = self.plugin_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.plugin_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.plugin_table.setAlternatingRowColors(True)
        self.plugin_table.itemSelectionChanged.connect(self.on_plugin_selected)
        
        list_layout.addWidget(self.plugin_table)
        splitter.addWidget(list_group)
        
        # 插件详情组
        details_group = QGroupBox("插件详情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(120)
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        splitter.addWidget(details_group)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 3)
        splitter.setStretchFactor(1, 1)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_plugin_list)
        button_layout.addWidget(refresh_btn)
        
        reload_btn = QPushButton("重载")
        reload_btn.clicked.connect(self.reload_selected_plugin)
        button_layout.addWidget(reload_btn)
        
        layout.addLayout(button_layout)
        
        # 统计信息
        self.stats_label = QLabel("统计信息: 加载中...")
        self.stats_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(self.stats_label)
        
        # 初始化数据
        self.refresh_plugin_list()
        
        return panel
    
    def refresh_plugin_list(self):
        """刷新插件列表"""
        if not self.plugin_manager:
            return
        
        try:
            # 获取所有插件
            all_plugins = self.plugin_manager.get_all_plugins()
            
            # 清空表格
            self.plugin_table.setRowCount(0)
            
            # 添加插件数据
            for plugin_id, plugin_info in all_plugins.items():
                row = self.plugin_table.rowCount()
                self.plugin_table.insertRow(row)
                
                # 名称
                name_item = QTableWidgetItem(plugin_info.get('name', plugin_id))
                name_item.setData(Qt.UserRole, plugin_id)  # 存储插件ID
                self.plugin_table.setItem(row, 0, name_item)
                
                # 版本
                version_item = QTableWidgetItem(plugin_info.get('version', '未知'))
                self.plugin_table.setItem(row, 1, version_item)
                
                # 状态
                state = plugin_info.get('state', '未知')
                state_item = QTableWidgetItem(state)
                
                # 根据状态设置颜色
                if state == 'active':
                    state_item.setBackground(Qt.green)
                elif state == 'error':
                    state_item.setBackground(Qt.red)
                elif state == 'loaded':
                    state_item.setBackground(Qt.yellow)
                
                self.plugin_table.setItem(row, 2, state_item)
                
                # 作者
                author_item = QTableWidgetItem(plugin_info.get('author', '未知'))
                self.plugin_table.setItem(row, 3, author_item)
            
            # 更新统计信息
            self.update_stats()
            
            print(f"插件列表已刷新，共 {len(all_plugins)} 个插件")
            
        except Exception as e:
            print(f"刷新插件列表失败: {e}")
    
    def on_plugin_selected(self):
        """插件选择事件处理"""
        current_row = self.plugin_table.currentRow()
        if current_row < 0:
            self.details_text.clear()
            return
        
        # 获取插件ID
        name_item = self.plugin_table.item(current_row, 0)
        if not name_item:
            return
        
        plugin_id = name_item.data(Qt.UserRole)
        if not plugin_id or not self.plugin_manager:
            return
        
        # 获取插件详细信息
        plugin_info = self.plugin_manager.get_plugin_status(plugin_id)
        if not plugin_info:
            return
        
        # 显示详细信息
        details = f"""插件ID: {plugin_id}
名称: {plugin_info.get('name', '未知')}
版本: {plugin_info.get('version', '未知')}
描述: {plugin_info.get('description', '无描述')}
作者: {plugin_info.get('author', '未知')}
状态: {plugin_info.get('state', '未知')}
路径: {plugin_info.get('path', '未知')}
加载时间: {plugin_info.get('load_time', 0):.3f}s
激活时间: {plugin_info.get('activate_time', 0):.3f}s
依赖: {', '.join(plugin_info.get('dependencies', []))}
错误信息: {plugin_info.get('error_message', '无')}"""
        
        self.details_text.setPlainText(details)
    
    def reload_selected_plugin(self):
        """重载选中的插件"""
        current_row = self.plugin_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(None, "警告", "请先选择一个插件")
            return
        
        # 获取插件ID
        name_item = self.plugin_table.item(current_row, 0)
        if not name_item:
            return
        
        plugin_id = name_item.data(Qt.UserRole)
        if not plugin_id or not self.plugin_manager:
            return
        
        try:
            # 重载插件
            success = self.plugin_manager.reload_plugin(plugin_id)
            
            if success:
                QMessageBox.information(None, "成功", f"插件 {plugin_id} 重载成功")
                self.refresh_plugin_list()
            else:
                QMessageBox.warning(None, "失败", f"插件 {plugin_id} 重载失败")
                
        except Exception as e:
            QMessageBox.critical(None, "错误", f"重载插件时出错: {e}")
    
    def update_stats(self):
        """更新统计信息"""
        if not self.plugin_manager:
            return
        
        try:
            stats = self.plugin_manager.get_stats()
            
            stats_text = (f"总计: {stats.get('total_plugins', 0)} | "
                         f"激活: {stats.get('active_plugins', 0)} | "
                         f"失败: {stats.get('failed_plugins', 0)}")
            
            self.stats_label.setText(f"统计信息: {stats_text}")
            
        except Exception as e:
            self.stats_label.setText(f"统计信息: 获取失败 - {e}")
    
    def setup_auto_refresh(self):
        """设置自动刷新"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_plugin_list)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
    
    def on_plugin_event(self, event_type, data):
        """插件事件处理"""
        print(f"插件管理器UI收到插件事件: {event_type}")
        
        # 延迟刷新，避免频繁更新
        if hasattr(self, 'refresh_timer') and self.refresh_timer:
            self.refresh_timer.stop()
            self.refresh_timer.start(1000)  # 1秒后刷新
    
    def get_properties(self):
        """获取插件属性"""
        plugin_count = self.plugin_table.rowCount() if hasattr(self, 'plugin_table') else 0
        
        return {
            '插件信息': {
                '名称': {'type': 'string', 'value': '插件管理器UI'},
                '版本': {'type': 'string', 'value': '1.0.0'},
                '状态': {'type': 'string', 'value': '已激活'}
            },
            '统计信息': {
                '管理的插件数': {'type': 'int', 'value': plugin_count},
                '自动刷新': {'type': 'bool', 'value': True},
                '刷新间隔': {'type': 'string', 'value': '5秒'}
            }
        }
    
    def deactivate(self):
        """停用插件"""
        print("插件管理器UI已停用")
        
        # 停止定时器
        if self.refresh_timer:
            self.refresh_timer.stop()
            self.refresh_timer = None
        
        # 清理资源
        if self.context:
            self.context.cleanup()
