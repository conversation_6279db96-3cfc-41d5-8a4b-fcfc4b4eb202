#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
插件系统演示脚本

该脚本演示插件系统的完整功能，包括：
- 插件发现和加载
- 事件系统
- 命令系统
- UI扩展
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

def demo_plugin_discovery():
    """演示插件发现功能"""
    print("🔍 插件发现演示")
    print("=" * 40)
    
    from tdpv.plugin import PluginManager
    
    # 创建插件管理器
    manager = PluginManager(['tdpv/plugins'])
    
    # 发现插件
    discovered = manager.discover_plugins()
    print(f"发现的插件: {discovered}")
    
    # 显示插件详情
    for plugin_id in discovered:
        info = manager.get_plugin_status(plugin_id)
        if info:
            print(f"\n📦 {plugin_id}")
            print(f"   名称: {info['name']}")
            print(f"   版本: {info['version']}")
            print(f"   描述: {info['description']}")
            print(f"   作者: {info['author']}")
            print(f"   状态: {info['state']}")
    
    return manager


def demo_plugin_loading(manager):
    """演示插件加载功能"""
    print("\n🚀 插件加载演示")
    print("=" * 40)
    
    # 加载所有插件
    load_success, load_failure = manager.load_all_plugins()
    print(f"加载结果: 成功 {load_success}, 失败 {load_failure}")
    
    # 显示加载后的状态
    all_plugins = manager.get_all_plugins()
    for plugin_id, info in all_plugins.items():
        print(f"📦 {plugin_id}: {info['state']}")
        if info.get('load_time'):
            print(f"   加载耗时: {info['load_time']:.3f}s")


def demo_plugin_activation(manager):
    """演示插件激活功能"""
    print("\n⚡ 插件激活演示")
    print("=" * 40)
    
    # 激活所有插件
    activate_success, activate_failure = manager.activate_all_plugins()
    print(f"激活结果: 成功 {activate_success}, 失败 {activate_failure}")
    
    # 显示激活后的状态
    active_plugins = manager.get_active_plugins()
    print(f"激活的插件: {active_plugins}")
    
    # 显示详细统计
    stats = manager.get_stats()
    print(f"统计信息: {stats}")


def demo_event_system():
    """演示事件系统功能"""
    print("\n📡 事件系统演示")
    print("=" * 40)
    
    from tdpv.plugin import get_event_bus, SystemEvents, FileEvents
    
    event_bus = get_event_bus()
    
    # 事件收集器
    received_events = []
    
    def event_collector(event_type, data):
        received_events.append((event_type, data))
        print(f"📨 收到事件: {event_type} - {data}")
    
    # 订阅不同类型的事件
    sub1 = event_bus.subscribe("app.*", event_collector)
    sub2 = event_bus.subscribe("file.*", event_collector)
    sub3 = event_bus.subscribe("plugin.*", event_collector)
    
    print("已订阅事件模式: app.*, file.*, plugin.*")
    
    # 发布一些测试事件
    print("\n发布测试事件:")
    event_bus.publish(SystemEvents.APP_STARTED, {"version": "1.0.0"})
    event_bus.publish(FileEvents.FILE_OPENED, "/path/to/file.txt")
    event_bus.publish("plugin.demo.test", {"message": "测试消息"})
    
    # 等待事件处理
    time.sleep(0.1)
    
    print(f"\n总共收到 {len(received_events)} 个事件")
    
    # 清理订阅
    event_bus.unsubscribe(sub1)
    event_bus.unsubscribe(sub2)
    event_bus.unsubscribe(sub3)


def demo_command_system():
    """演示命令系统功能"""
    print("\n⌨️ 命令系统演示")
    print("=" * 40)
    
    from tdpv.plugin import get_command_registry
    
    registry = get_command_registry()
    
    # 注册一些演示命令
    def hello_command(name="World"):
        return f"Hello, {name}!"
    
    def calculate_command(a, b, operation="add"):
        if operation == "add":
            return a + b
        elif operation == "multiply":
            return a * b
        else:
            return "不支持的操作"
    
    # 注册命令
    registry.register_command("demo.hello", hello_command, "问候命令", "Ctrl+H", "demo_plugin")
    registry.register_command("demo.calc", calculate_command, "计算命令", "Ctrl+C", "demo_plugin")
    
    print("已注册命令:")
    all_commands = registry.get_all_commands()
    for cmd_id, cmd_info in all_commands.items():
        print(f"  {cmd_id}: {cmd_info['description']} ({cmd_info['shortcut']})")
    
    # 执行命令
    print("\n执行命令:")
    result1 = registry.execute_command("demo.hello")
    print(f"demo.hello() = {result1}")
    
    result2 = registry.execute_command("demo.hello", "插件系统")
    print(f"demo.hello('插件系统') = {result2}")
    
    result3 = registry.execute_command("demo.calc", 10, 5, operation="add")
    print(f"demo.calc(10, 5, 'add') = {result3}")
    
    result4 = registry.execute_command("demo.calc", 10, 5, operation="multiply")
    print(f"demo.calc(10, 5, 'multiply') = {result4}")


def demo_plugin_scaffold():
    """演示插件脚手架功能"""
    print("\n🛠️ 插件脚手架演示")
    print("=" * 40)
    
    from tdpv.plugin.tools import PluginScaffold
    
    scaffold = PluginScaffold("demo_plugins")
    
    # 显示可用模板
    print("可用的插件模板:")
    scaffold.list_templates()
    
    # 创建演示插件
    print("\n创建演示插件:")
    plugins_to_create = [
        ("demo_basic", "basic", "基础演示插件"),
        ("demo_ui", "ui", "UI演示插件"),
        ("demo_command", "command", "命令演示插件")
    ]
    
    for name, plugin_type, desc in plugins_to_create:
        success = scaffold.create_plugin(name, plugin_type, desc, "Demo Author")
        if success:
            print(f"✅ {name} 创建成功")
        else:
            print(f"❌ {name} 创建失败")
    
    # 列出创建的文件
    import pathlib
    demo_dir = pathlib.Path("demo_plugins")
    if demo_dir.exists():
        print(f"\n创建的插件目录:")
        for plugin_dir in demo_dir.iterdir():
            if plugin_dir.is_dir():
                print(f"📁 {plugin_dir.name}/")
                for file in plugin_dir.iterdir():
                    print(f"   📄 {file.name}")
    
    # 清理演示文件
    import shutil
    if demo_dir.exists():
        shutil.rmtree(demo_dir)
        print("\n🧹 演示文件已清理")


def demo_hot_reload():
    """演示热重载功能"""
    print("\n🔥 热重载演示")
    print("=" * 40)
    
    print("热重载功能需要在实际应用中测试")
    print("功能包括:")
    print("- 文件变更监控")
    print("- 自动重载插件")
    print("- 保持应用状态")
    print("- 错误隔离")


def main():
    """主演示函数"""
    print("🎭 TDPV 插件系统完整演示")
    print("=" * 60)

    # 创建QApplication（插件可能需要）
    from PySide6.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
        print("✅ QApplication 已创建")

    try:
        # 1. 插件发现
        manager = demo_plugin_discovery()
        
        # 2. 插件加载
        demo_plugin_loading(manager)
        
        # 3. 插件激活
        demo_plugin_activation(manager)
        
        # 4. 事件系统
        demo_event_system()
        
        # 5. 命令系统
        demo_command_system()
        
        # 6. 插件脚手架
        demo_plugin_scaffold()
        
        # 7. 热重载
        demo_hot_reload()
        
        print("\n🎉 演示完成！")
        print("=" * 60)
        print("插件系统功能:")
        print("✅ 插件发现和管理")
        print("✅ 生命周期控制")
        print("✅ 事件总线通信")
        print("✅ 命令系统")
        print("✅ UI扩展接口")
        print("✅ 开发工具支持")
        print("✅ 热重载机制")
        print("✅ 错误隔离")
        
        # 清理
        if manager:
            manager.shutdown()
        
        return 0
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
