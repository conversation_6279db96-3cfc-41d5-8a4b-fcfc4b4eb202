#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件类型定义模块

该模块定义了插件系统中使用的标准事件类型和事件数据结构。
"""

from enum import Enum
from typing import Any, Dict, Optional
from dataclasses import dataclass


class SystemEvents:
    """系统级事件类型"""
    
    # 应用程序生命周期
    APP_STARTING = "app.starting"
    APP_STARTED = "app.started"
    APP_STOPPING = "app.stopping"
    APP_STOPPED = "app.stopped"
    
    # 插件系统
    PLUGIN_LOADING = "plugin.loading"
    PLUGIN_LOADED = "plugin.loaded"
    PLUGIN_ACTIVATING = "plugin.activating"
    PLUGIN_ACTIVATED = "plugin.activated"
    PLUGIN_DEACTIVATING = "plugin.deactivating"
    PLUGIN_DEACTIVATED = "plugin.deactivated"
    PLUGIN_ERROR = "plugin.error"
    
    # 配置变更
    CONFIG_CHANGED = "config.changed"
    THEME_CHANGED = "theme.changed"


class FileEvents:
    """文件相关事件类型"""
    
    # 文件操作
    FILE_OPENING = "file.opening"
    FILE_OPENED = "file.opened"
    FILE_CLOSING = "file.closing"
    FILE_CLOSED = "file.closed"
    FILE_SAVING = "file.saving"
    FILE_SAVED = "file.saved"
    FILE_MODIFIED = "file.modified"
    FILE_DELETED = "file.deleted"
    FILE_RENAMED = "file.renamed"
    
    # 文件夹操作
    FOLDER_OPENING = "folder.opening"
    FOLDER_OPENED = "folder.opened"
    FOLDER_CLOSING = "folder.closing"
    FOLDER_CLOSED = "folder.closed"
    FOLDER_CREATED = "folder.created"
    FOLDER_DELETED = "folder.deleted"
    FOLDER_RENAMED = "folder.renamed"
    
    # 文件系统监控
    FILE_SYSTEM_CHANGED = "filesystem.changed"
    FILE_WATCHER_STARTED = "filewatcher.started"
    FILE_WATCHER_STOPPED = "filewatcher.stopped"


class UIEvents:
    """UI相关事件类型"""
    
    # 窗口事件
    WINDOW_RESIZED = "window.resized"
    WINDOW_MOVED = "window.moved"
    WINDOW_MINIMIZED = "window.minimized"
    WINDOW_MAXIMIZED = "window.maximized"
    WINDOW_RESTORED = "window.restored"
    
    # 标签页事件
    TAB_CREATED = "tab.created"
    TAB_ACTIVATED = "tab.activated"
    TAB_CLOSED = "tab.closed"
    TAB_MOVED = "tab.moved"
    
    # 侧边栏事件
    SIDEBAR_EXPANDED = "sidebar.expanded"
    SIDEBAR_COLLAPSED = "sidebar.collapsed"
    SIDEBAR_PLUGIN_SELECTED = "sidebar.plugin_selected"
    
    # 属性面板事件
    PROPERTY_PANEL_SHOWN = "property_panel.shown"
    PROPERTY_PANEL_HIDDEN = "property_panel.hidden"
    PROPERTY_CHANGED = "property.changed"
    
    # 菜单和工具栏
    MENU_ITEM_CLICKED = "menu.item_clicked"
    TOOLBAR_BUTTON_CLICKED = "toolbar.button_clicked"


class CommandEvents:
    """命令相关事件类型"""
    
    # 命令执行
    COMMAND_REGISTERED = "command.registered"
    COMMAND_UNREGISTERED = "command.unregistered"
    COMMAND_EXECUTING = "command.executing"
    COMMAND_EXECUTED = "command.executed"
    COMMAND_FAILED = "command.failed"
    
    # 快捷键
    SHORTCUT_REGISTERED = "shortcut.registered"
    SHORTCUT_TRIGGERED = "shortcut.triggered"


class DataEvents:
    """数据相关事件类型"""
    
    # 数据变更
    DATA_LOADING = "data.loading"
    DATA_LOADED = "data.loaded"
    DATA_SAVING = "data.saving"
    DATA_SAVED = "data.saved"
    DATA_CHANGED = "data.changed"
    DATA_DELETED = "data.deleted"
    
    # 数据验证
    DATA_VALIDATING = "data.validating"
    DATA_VALIDATED = "data.validated"
    DATA_VALIDATION_FAILED = "data.validation_failed"


@dataclass
class PluginEventData:
    """插件事件数据"""
    plugin_id: str
    plugin_name: str
    plugin_version: str
    plugin_path: Optional[str] = None
    error_message: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class FileEventData:
    """文件事件数据"""
    file_path: str
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    file_type: Optional[str] = None
    encoding: Optional[str] = None
    is_directory: bool = False
    old_path: Optional[str] = None  # 用于重命名事件
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class UIEventData:
    """UI事件数据"""
    widget_id: Optional[str] = None
    widget_type: Optional[str] = None
    position: Optional[tuple] = None
    size: Optional[tuple] = None
    value: Optional[Any] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class CommandEventData:
    """命令事件数据"""
    command_id: str
    command_name: Optional[str] = None
    arguments: Optional[Dict[str, Any]] = None
    result: Optional[Any] = None
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class DataEventData:
    """数据事件数据"""
    data_type: str
    data_id: Optional[str] = None
    data_content: Optional[Any] = None
    data_size: Optional[int] = None
    validation_errors: Optional[list] = None
    additional_data: Optional[Dict[str, Any]] = None


class EventPatterns:
    """常用事件模式"""
    
    # 通配符模式
    ALL_EVENTS = "*"
    ALL_FILE_EVENTS = "file.*"
    ALL_FOLDER_EVENTS = "folder.*"
    ALL_UI_EVENTS = "ui.*"
    ALL_COMMAND_EVENTS = "command.*"
    ALL_PLUGIN_EVENTS = "plugin.*"
    ALL_DATA_EVENTS = "data.*"
    
    # 组合模式
    ALL_OPEN_EVENTS = "*.opening"
    ALL_CLOSE_EVENTS = "*.closing"
    ALL_SAVE_EVENTS = "*.saving"
    ALL_ERROR_EVENTS = "*.error"
    ALL_CHANGED_EVENTS = "*.changed"


def create_plugin_event_data(plugin_id: str, plugin_name: str, 
                           plugin_version: str, **kwargs) -> PluginEventData:
    """创建插件事件数据
    
    Args:
        plugin_id: 插件ID
        plugin_name: 插件名称
        plugin_version: 插件版本
        **kwargs: 其他参数
        
    Returns:
        PluginEventData: 插件事件数据
    """
    return PluginEventData(
        plugin_id=plugin_id,
        plugin_name=plugin_name,
        plugin_version=plugin_version,
        **kwargs
    )


def create_file_event_data(file_path: str, **kwargs) -> FileEventData:
    """创建文件事件数据
    
    Args:
        file_path: 文件路径
        **kwargs: 其他参数
        
    Returns:
        FileEventData: 文件事件数据
    """
    import os
    
    file_name = os.path.basename(file_path) if file_path else None
    is_directory = os.path.isdir(file_path) if os.path.exists(file_path) else False
    
    return FileEventData(
        file_path=file_path,
        file_name=file_name,
        is_directory=is_directory,
        **kwargs
    )


def create_ui_event_data(**kwargs) -> UIEventData:
    """创建UI事件数据
    
    Args:
        **kwargs: 事件参数
        
    Returns:
        UIEventData: UI事件数据
    """
    return UIEventData(**kwargs)


def create_command_event_data(command_id: str, **kwargs) -> CommandEventData:
    """创建命令事件数据
    
    Args:
        command_id: 命令ID
        **kwargs: 其他参数
        
    Returns:
        CommandEventData: 命令事件数据
    """
    return CommandEventData(command_id=command_id, **kwargs)


def create_data_event_data(data_type: str, **kwargs) -> DataEventData:
    """创建数据事件数据
    
    Args:
        data_type: 数据类型
        **kwargs: 其他参数
        
    Returns:
        DataEventData: 数据事件数据
    """
    return DataEventData(data_type=data_type, **kwargs)


# 事件类型注册表
EVENT_REGISTRY = {
    # 系统事件
    **{getattr(SystemEvents, attr): attr for attr in dir(SystemEvents) 
       if not attr.startswith('_')},
    
    # 文件事件
    **{getattr(FileEvents, attr): attr for attr in dir(FileEvents) 
       if not attr.startswith('_')},
    
    # UI事件
    **{getattr(UIEvents, attr): attr for attr in dir(UIEvents) 
       if not attr.startswith('_')},
    
    # 命令事件
    **{getattr(CommandEvents, attr): attr for attr in dir(CommandEvents) 
       if not attr.startswith('_')},
    
    # 数据事件
    **{getattr(DataEvents, attr): attr for attr in dir(DataEvents) 
       if not attr.startswith('_')},
}


def is_valid_event_type(event_type: str) -> bool:
    """检查事件类型是否有效
    
    Args:
        event_type: 事件类型
        
    Returns:
        bool: 是否为有效的事件类型
    """
    return event_type in EVENT_REGISTRY or '*' in event_type


def get_event_description(event_type: str) -> Optional[str]:
    """获取事件类型描述
    
    Args:
        event_type: 事件类型
        
    Returns:
        Optional[str]: 事件描述，如果不存在则返回None
    """
    return EVENT_REGISTRY.get(event_type)


def get_all_event_types() -> list:
    """获取所有已注册的事件类型
    
    Returns:
        list: 所有事件类型列表
    """
    return list(EVENT_REGISTRY.keys())
