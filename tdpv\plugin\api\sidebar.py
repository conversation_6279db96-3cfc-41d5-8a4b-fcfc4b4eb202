#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
侧边栏API模块

该模块提供插件系统的侧边栏扩展接口，允许插件添加图标和面板。
"""

import logging
from typing import Any, Dict, List, Optional
from PySide6.QtWidgets import QWidget, QLabel
from PySide6.QtCore import QObject, Signal

from ..events import get_event_bus, UIEvents, create_ui_event_data

logger = logging.getLogger(__name__)


class SidebarItem:
    """侧边栏项目"""
    
    def __init__(self, item_id: str, icon_widget: QWidget, 
                 panel_widget: Optional[QWidget] = None, plugin_id: str = ""):
        self.item_id = item_id
        self.icon_widget = icon_widget
        self.panel_widget = panel_widget
        self.plugin_id = plugin_id
        self.is_active = False
        self.properties = {}
    
    def set_property(self, key: str, value: Any) -> None:
        """设置属性"""
        self.properties[key] = value
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取属性"""
        return self.properties.get(key, default)
    
    def get_properties(self) -> Dict[str, Any]:
        """获取所有属性"""
        return {
            '基本信息': {
                'ID': {'type': 'string', 'value': self.item_id},
                '插件ID': {'type': 'string', 'value': self.plugin_id},
                '状态': {'type': 'string', 'value': '激活' if self.is_active else '未激活'}
            },
            '自定义属性': {
                key: {'type': 'string', 'value': str(value)}
                for key, value in self.properties.items()
            }
        }


class SidebarManager(QObject):
    """侧边栏管理器"""
    
    # 信号
    item_added = Signal(str)  # 项目添加信号
    item_removed = Signal(str)  # 项目移除信号
    item_activated = Signal(str, object)  # 项目激活信号
    
    def __init__(self):
        super().__init__()
        self._items: Dict[str, SidebarItem] = {}
        self._plugin_items: Dict[str, List[str]] = {}  # 插件到项目的映射
        self._event_bus = get_event_bus()
        self._main_window = None
        
        # 获取主窗口引用
        self._get_main_window()
    
    def _get_main_window(self):
        """获取主窗口实例"""
        try:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if hasattr(widget, 'sidebar') and hasattr(widget, 'property_panel'):
                        self._main_window = widget
                        break
        except Exception as e:
            logger.warning(f"无法获取主窗口: {e}")
    
    def add_item(self, item_id: str, icon_widget: QWidget,
                panel_widget: Optional[QWidget] = None, plugin_id: str = "") -> bool:
        """添加侧边栏项目
        
        Args:
            item_id: 项目ID
            icon_widget: 图标控件
            panel_widget: 面板控件
            plugin_id: 插件ID
            
        Returns:
            bool: 是否添加成功
        """
        if item_id in self._items:
            logger.warning(f"侧边栏项目已存在: {item_id}")
            return False
        
        try:
            # 创建侧边栏项目
            item = SidebarItem(item_id, icon_widget, panel_widget, plugin_id)
            self._items[item_id] = item
            
            # 记录插件项目
            if plugin_id:
                if plugin_id not in self._plugin_items:
                    self._plugin_items[plugin_id] = []
                self._plugin_items[plugin_id].append(item_id)
            
            # 添加到实际的侧边栏
            if self._main_window and hasattr(self._main_window, 'sidebar'):
                sidebar = self._main_window.sidebar
                success = sidebar.add_plugin(item_id, icon_widget, panel_widget)
                if not success:
                    # 回滚
                    del self._items[item_id]
                    if plugin_id and plugin_id in self._plugin_items:
                        self._plugin_items[plugin_id].remove(item_id)
                        if not self._plugin_items[plugin_id]:
                            del self._plugin_items[plugin_id]
                    return False
            
            # 发布事件
            event_data = create_ui_event_data(
                widget_id=item_id,
                widget_type='sidebar_item',
                additional_data={'plugin_id': plugin_id}
            )
            self._event_bus.publish(UIEvents.SIDEBAR_PLUGIN_SELECTED, event_data)
            
            # 发射信号
            self.item_added.emit(item_id)
            
            logger.debug(f"添加侧边栏项目: {item_id} (插件: {plugin_id})")
            return True
            
        except Exception as e:
            logger.error(f"添加侧边栏项目失败: {item_id}, 错误: {e}")
            return False
    
    def remove_item(self, item_id: str) -> bool:
        """移除侧边栏项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            bool: 是否移除成功
        """
        if item_id not in self._items:
            logger.warning(f"侧边栏项目不存在: {item_id}")
            return False
        
        try:
            item = self._items[item_id]
            
            # 从实际的侧边栏移除
            if self._main_window and hasattr(self._main_window, 'sidebar'):
                sidebar = self._main_window.sidebar
                sidebar.remove_plugin(item_id)
            
            # 移除记录
            del self._items[item_id]
            
            # 移除插件项目记录
            if item.plugin_id and item.plugin_id in self._plugin_items:
                plugin_items = self._plugin_items[item.plugin_id]
                if item_id in plugin_items:
                    plugin_items.remove(item_id)
                if not plugin_items:
                    del self._plugin_items[item.plugin_id]
            
            # 发射信号
            self.item_removed.emit(item_id)
            
            logger.debug(f"移除侧边栏项目: {item_id}")
            return True
            
        except Exception as e:
            logger.error(f"移除侧边栏项目失败: {item_id}, 错误: {e}")
            return False
    
    def update_item_panel(self, item_id: str, panel_widget: QWidget) -> bool:
        """更新项目面板
        
        Args:
            item_id: 项目ID
            panel_widget: 新的面板控件
            
        Returns:
            bool: 是否更新成功
        """
        if item_id not in self._items:
            logger.warning(f"侧边栏项目不存在: {item_id}")
            return False
        
        try:
            item = self._items[item_id]
            item.panel_widget = panel_widget
            
            # 更新实际的侧边栏
            if self._main_window and hasattr(self._main_window, 'sidebar'):
                sidebar = self._main_window.sidebar
                if hasattr(sidebar, 'update_plugin_panel'):
                    sidebar.update_plugin_panel(item_id, panel_widget)
            
            logger.debug(f"更新侧边栏项目面板: {item_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新侧边栏项目面板失败: {item_id}, 错误: {e}")
            return False
    
    def activate_item(self, item_id: str) -> bool:
        """激活侧边栏项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            bool: 是否激活成功
        """
        if item_id not in self._items:
            logger.warning(f"侧边栏项目不存在: {item_id}")
            return False
        
        try:
            # 先取消其他项目的激活状态
            for other_id, other_item in self._items.items():
                if other_item.is_active:
                    other_item.is_active = False
            
            # 激活指定项目
            item = self._items[item_id]
            item.is_active = True
            
            # 发射信号
            self.item_activated.emit(item_id, item)
            
            logger.debug(f"激活侧边栏项目: {item_id}")
            return True
            
        except Exception as e:
            logger.error(f"激活侧边栏项目失败: {item_id}, 错误: {e}")
            return False
    
    def get_item(self, item_id: str) -> Optional[SidebarItem]:
        """获取侧边栏项目
        
        Args:
            item_id: 项目ID
            
        Returns:
            Optional[SidebarItem]: 侧边栏项目，如果不存在则返回None
        """
        return self._items.get(item_id)
    
    def get_all_items(self) -> Dict[str, SidebarItem]:
        """获取所有侧边栏项目
        
        Returns:
            Dict[str, SidebarItem]: 所有侧边栏项目
        """
        return self._items.copy()
    
    def get_plugin_items(self, plugin_id: str) -> List[str]:
        """获取插件的所有侧边栏项目
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            List[str]: 项目ID列表
        """
        return self._plugin_items.get(plugin_id, []).copy()
    
    def remove_plugin_items(self, plugin_id: str) -> int:
        """移除插件的所有侧边栏项目
        
        Args:
            plugin_id: 插件ID
            
        Returns:
            int: 移除的项目数量
        """
        items = self.get_plugin_items(plugin_id)
        count = 0
        
        for item_id in items:
            if self.remove_item(item_id):
                count += 1
        
        logger.debug(f"移除插件侧边栏项目: {plugin_id}, 数量: {count}")
        return count
    
    def get_active_item(self) -> Optional[str]:
        """获取当前激活的项目ID
        
        Returns:
            Optional[str]: 激活的项目ID，如果没有则返回None
        """
        for item_id, item in self._items.items():
            if item.is_active:
                return item_id
        return None
    
    def clear(self) -> None:
        """清空所有侧边栏项目"""
        item_ids = list(self._items.keys())
        for item_id in item_ids:
            self.remove_item(item_id)
        
        self._items.clear()
        self._plugin_items.clear()
        logger.debug("清空所有侧边栏项目")


# 全局侧边栏管理器实例
_global_sidebar_manager: Optional[SidebarManager] = None


def get_sidebar_manager() -> SidebarManager:
    """获取全局侧边栏管理器实例
    
    Returns:
        SidebarManager: 全局侧边栏管理器实例
    """
    global _global_sidebar_manager
    if _global_sidebar_manager is None:
        _global_sidebar_manager = SidebarManager()
    return _global_sidebar_manager


def add_sidebar_icon(item_id: str, icon_widget: QWidget, plugin_id: str = "") -> bool:
    """添加侧边栏图标（便捷函数）
    
    Args:
        item_id: 项目ID
        icon_widget: 图标控件
        plugin_id: 插件ID
        
    Returns:
        bool: 是否添加成功
    """
    return get_sidebar_manager().add_item(item_id, icon_widget, None, plugin_id)


def add_sidebar_panel(item_id: str, panel_widget: QWidget) -> bool:
    """添加侧边栏面板（便捷函数）
    
    Args:
        item_id: 项目ID
        panel_widget: 面板控件
        
    Returns:
        bool: 是否添加成功
    """
    return get_sidebar_manager().update_item_panel(item_id, panel_widget)


def remove_sidebar_item(item_id: str) -> bool:
    """移除侧边栏项目（便捷函数）
    
    Args:
        item_id: 项目ID
        
    Returns:
        bool: 是否移除成功
    """
    return get_sidebar_manager().remove_item(item_id)
