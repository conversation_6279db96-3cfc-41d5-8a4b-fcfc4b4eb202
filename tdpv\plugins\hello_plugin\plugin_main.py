#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Hello World 示例插件

这是一个简单的示例插件，展示了插件系统的基本功能：
- 侧边栏图标和面板
- 命令注册
- 事件订阅和发布
- 属性面板集成
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QTextEdit
from PySide6.QtCore import Qt


class HelloPlugin:
    """Hello World插件类"""
    
    def __init__(self):
        self.context = None
        self.click_count = 0
        self.messages = []
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        
        print("Hello插件已激活")
        
        # 创建UI组件
        icon_widget = self.create_icon_widget()
        panel_widget = self.create_panel_widget()
        
        # 注册侧边栏组件
        context.add_sidebar_icon("hello_plugin", icon_widget)
        context.add_sidebar_panel("hello_plugin", panel_widget)
        
        # 注册命令
        context.register_command("hello.say_hello", self.say_hello, "说Hello", "Ctrl+H")
        context.register_command("hello.show_info", self.show_info, "显示插件信息")
        context.register_command("hello.clear_messages", self.clear_messages, "清空消息")
        
        # 订阅事件
        context.subscribe("app.*", self.on_app_event)
        context.subscribe("file.*", self.on_file_event)
        
        # 发布插件激活事件
        context.publish("plugin.hello.activated", {
            'plugin_id': 'hello_plugin',
            'message': 'Hello插件已成功激活！'
        })
    
    def create_icon_widget(self):
        """创建侧边栏图标"""
        icon_widget = QLabel("👋")
        icon_widget.setAlignment(Qt.AlignCenter)
        icon_widget.setToolTip("Hello插件")
        icon_widget.setFixedSize(32, 32)
        icon_widget.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
                font-size: 18px;
            }
            QLabel:hover {
                background-color: rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
        """)
        return icon_widget
    
    def create_panel_widget(self):
        """创建侧边栏面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("Hello插件")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel("这是一个示例插件，展示基本功能。")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(desc_label)
        
        # 点击计数器
        self.counter_label = QLabel(f"按钮点击次数: {self.click_count}")
        layout.addWidget(self.counter_label)
        
        # 按钮
        hello_button = QPushButton("说Hello")
        hello_button.clicked.connect(self.say_hello)
        layout.addWidget(hello_button)
        
        info_button = QPushButton("显示信息")
        info_button.clicked.connect(self.show_info)
        layout.addWidget(info_button)
        
        clear_button = QPushButton("清空消息")
        clear_button.clicked.connect(self.clear_messages)
        layout.addWidget(clear_button)
        
        # 消息显示区域
        messages_label = QLabel("消息:")
        layout.addWidget(messages_label)
        
        self.messages_text = QTextEdit()
        self.messages_text.setMaximumHeight(100)
        self.messages_text.setReadOnly(True)
        layout.addWidget(self.messages_text)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def say_hello(self):
        """说Hello命令处理"""
        self.click_count += 1
        message = f"Hello! 这是第 {self.click_count} 次问候。"
        
        # 更新UI
        if hasattr(self, 'counter_label'):
            self.counter_label.setText(f"按钮点击次数: {self.click_count}")
        
        # 添加消息
        self.add_message(message)
        
        # 发布事件
        if self.context:
            self.context.publish("hello.said", {
                'message': message,
                'count': self.click_count
            })
        
        print(message)
    
    def show_info(self):
        """显示插件信息"""
        info = f"""
插件名称: Hello插件
版本: 1.0.0
状态: 已激活
点击次数: {self.click_count}
消息数量: {len(self.messages)}
        """.strip()
        
        self.add_message(info)
        print("插件信息:")
        print(info)
    
    def clear_messages(self):
        """清空消息"""
        self.messages.clear()
        if hasattr(self, 'messages_text'):
            self.messages_text.clear()
        
        self.add_message("消息已清空")
        print("消息已清空")
    
    def add_message(self, message):
        """添加消息到显示区域"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.messages.append(formatted_message)
        
        # 限制消息数量
        if len(self.messages) > 50:
            self.messages = self.messages[-50:]
        
        # 更新UI
        if hasattr(self, 'messages_text'):
            self.messages_text.append(formatted_message)
    
    def on_app_event(self, event_type, data):
        """应用程序事件处理"""
        self.add_message(f"应用事件: {event_type}")
        print(f"Hello插件收到应用事件: {event_type}")
    
    def on_file_event(self, event_type, data):
        """文件事件处理"""
        self.add_message(f"文件事件: {event_type} - {data}")
        print(f"Hello插件收到文件事件: {event_type} - {data}")
    
    def get_properties(self):
        """获取插件属性（用于属性面板显示）"""
        return {
            '基本信息': {
                '插件名称': {'type': 'string', 'value': 'Hello插件'},
                '版本': {'type': 'string', 'value': '1.0.0'},
                '状态': {'type': 'string', 'value': '已激活'},
                '作者': {'type': 'string', 'value': 'TDPV Team'}
            },
            '统计信息': {
                '点击次数': {'type': 'int', 'value': self.click_count},
                '消息数量': {'type': 'int', 'value': len(self.messages)},
                '是否活跃': {'type': 'bool', 'value': True}
            },
            '设置': {
                '自动清理': {'type': 'bool', 'value': False},
                '最大消息数': {'type': 'int', 'value': 50},
                '显示时间戳': {'type': 'bool', 'value': True}
            }
        }
    
    def set_property(self, prop_name, value):
        """设置插件属性"""
        self.add_message(f"属性已更改: {prop_name} = {value}")
        print(f"Hello插件属性更改: {prop_name} = {value}")
    
    def deactivate(self):
        """停用插件"""
        print("Hello插件已停用")
        
        # 发布插件停用事件
        if self.context:
            self.context.publish("plugin.hello.deactivated", {
                'plugin_id': 'hello_plugin',
                'final_click_count': self.click_count
            })
        
        # 清理资源
        if self.context:
            self.context.cleanup()
