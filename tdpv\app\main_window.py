#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主窗口模块

该模块实现了应用程序的主窗口，包含三部分布局：
- 左侧边栏（可折叠的插件栏）
- 中央区域（标签页工作区）
- 右侧属性面板（可隐藏）
"""

import os

from PySide6.QtWidgets import (
    QMainWindow, QTabWidget, QDockWidget, QWidget, QFileDialog,
    QVBoxLayout, QLabel
)
from PySide6.QtCore import Qt, Slot

from gui.widgets.sidebar import Sidebar
from gui.widgets.property_panel import PropertyPanel
from app.welcome_page import WelcomePage


class TabManager:
    """标签页管理器

    负责管理主窗口中的标签页，包括添加、删除和切换标签页。
    """

    def __init__(self, tab_widget: QTabWidget, parent: QMainWindow):
        """初始化标签页管理器

        Args:
            tab_widget: 标签页控件
            parent: 父窗口
        """
        self.tab_widget = tab_widget
        self.parent = parent

        # 设置标签页属性
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.setMovable(True)
        self.tab_widget.tabCloseRequested.connect(self.on_tab_close_requested)

    def add_welcome_tab(self) -> None:
        """添加欢迎标签页"""
        # 创建欢迎界面
        welcome_page = WelcomePage()

        # 连接欢迎界面的信号
        welcome_page.open_file.connect(self.parent.on_open_file)
        welcome_page.open_folder.connect(self.parent.on_open_folder)
        welcome_page.create_new_file.connect(self.parent.on_create_new_file)

        # 添加到标签页
        self.tab_widget.addTab(welcome_page, "欢迎")

    def add_file_tab(self, path: str) -> None:
        """添加文件标签页

        Args:
            path: 文件路径
        """
        # 创建标签页内容
        file_tab = QWidget()
        layout = QVBoxLayout(file_tab)

        # 显示文件路径
        file_label = QLabel(f"文件路径: {path}")
        layout.addWidget(file_label)

        # 添加到标签页，并切换到新标签页
        file_name = self._get_basename(path)
        index = self.tab_widget.addTab(file_tab, file_name)
        self.tab_widget.setCurrentIndex(index)

    def add_folder_tab(self, path: str) -> None:
        """添加文件夹标签页

        Args:
            path: 文件夹路径
        """
        # 创建标签页内容
        folder_tab = QWidget()
        layout = QVBoxLayout(folder_tab)

        # 显示文件夹路径
        folder_label = QLabel(f"文件夹路径: {path}")
        layout.addWidget(folder_label)

        # 添加到标签页，并切换到新标签页
        folder_name = self._get_basename(path)
        index = self.tab_widget.addTab(folder_tab, folder_name)
        self.tab_widget.setCurrentIndex(index)

    def add_new_file_tab(self) -> None:
        """添加新文件标签页"""
        # 创建标签页内容
        new_tab = QWidget()
        layout = QVBoxLayout(new_tab)
        label = QLabel("新建文件内容区域")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

        # 添加到标签页，并切换到新标签页
        index = self.tab_widget.addTab(new_tab, "新建文件")
        self.tab_widget.setCurrentIndex(index)

    @Slot(int)
    def on_tab_close_requested(self, index: int) -> None:
        """处理标签页关闭请求

        Args:
            index: 要关闭的标签页索引
        """
        # 如果只有一个标签页，不允许关闭
        if self.tab_widget.count() <= 1:
            return

        # 关闭标签页
        self.tab_widget.removeTab(index)

        # 如果关闭后没有标签页了，添加欢迎页
        if self.tab_widget.count() == 0:
            self.add_welcome_tab()

    def _get_basename(self, path: str) -> str:
        """获取路径的基本名称

        Args:
            path: 文件或文件夹路径

        Returns:
            基本名称
        """
        return os.path.basename(path) if path else "未命名"


class MainWindow(QMainWindow):
    """应用程序主窗口

    实现了应用程序的主界面，包含侧边栏、标签页和属性面板。
    """

    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 插件管理器
        self.plugin_manager = None

        # 初始化窗口属性
        self._init_window_properties()

        # 初始化标签页管理器
        self._init_tab_manager()

        # 初始化侧边栏
        self._init_sidebar()

        # 初始化属性面板
        self._init_property_panel()

    def _init_window_properties(self) -> None:
        """初始化窗口属性"""
        self.setWindowTitle("TDPV")
        self.setMinimumSize(1000, 600)

    def _init_tab_manager(self) -> None:
        """初始化标签页管理器"""
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        # 创建标签页管理器
        self.tab_manager = TabManager(self.tab_widget, self)

        # 添加默认标签页
        self.tab_manager.add_welcome_tab()

    def _init_sidebar(self) -> None:
        """初始化侧边栏"""
        # 创建侧边栏
        self.sidebar = Sidebar(self)
        self.addDockWidget(Qt.LeftDockWidgetArea, self.sidebar)

        # 连接侧边栏的信号
        self.sidebar.plugin_with_properties_selected.connect(self.on_plugin_with_properties_selected)
        self.sidebar.plugin_without_properties_selected.connect(self.on_plugin_without_properties_selected)

        # 设置侧边栏的宽度
        self.resizeDocks([self.sidebar], [250], Qt.Horizontal)

    def _init_property_panel(self) -> None:
        """初始化属性面板"""
        # 创建属性面板的停靠窗口
        self.property_dock = QDockWidget(self)

        # 设置特性标志，不包含标题栏
        self.property_dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable)
        self.property_dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)

        # 完全去除标题栏
        self.property_dock.setTitleBarWidget(QWidget())

        # 创建属性面板控件
        self.property_panel = PropertyPanel()
        self.property_dock.setWidget(self.property_panel)
        self.addDockWidget(Qt.RightDockWidgetArea, self.property_dock)

        # 默认隐藏属性栏
        self.property_dock.setVisible(False)

    @Slot(str)
    def on_open_file(self, path: str) -> None:
        """处理打开文件的请求

        Args:
            path: 文件路径，如果为空则显示文件选择对话框
        """
        # 如果没有提供路径，显示文件选择对话框
        if not path:
            path, _ = QFileDialog.getOpenFileName(
                self, "打开文件", "", "所有文件 (*.*)"
            )

        if path:
            # 添加文件标签页
            self.tab_manager.add_file_tab(path)
            print(f"打开文件: {path}")

    @Slot(str)
    def on_open_folder(self, path: str) -> None:
        """处理打开文件夹的请求

        Args:
            path: 文件夹路径，如果为空则显示文件夹选择对话框
        """
        # 如果没有提供路径，显示文件夹选择对话框
        if not path:
            path = QFileDialog.getExistingDirectory(
                self, "打开文件夹", ""
            )

        if path:
            # 添加文件夹标签页
            self.tab_manager.add_folder_tab(path)
            print(f"打开文件夹: {path}")

    @Slot()
    def on_create_new_file(self) -> None:
        """处理创建新文件的请求"""
        # 添加新文件标签页
        self.tab_manager.add_new_file_tab()
        print("创建新文件")

    @Slot(object)
    def on_plugin_with_properties_selected(self, plugin) -> None:
        """处理选择了带属性的插件

        Args:
            plugin: 选中的插件对象
        """
        # 显示属性栏
        self.property_dock.setVisible(True)

        # 设置属性面板的对象
        self.property_panel.set_object(plugin)

    @Slot()
    def on_plugin_without_properties_selected(self) -> None:
        """处理选择了不带属性的插件"""
        # 隐藏属性栏
        self.property_dock.setVisible(False)

        # 清除属性面板
        self.property_panel.clear()

    def set_plugin_manager(self, plugin_manager):
        """设置插件管理器

        Args:
            plugin_manager: 插件管理器实例
        """
        self.plugin_manager = plugin_manager

        # 如果侧边栏已初始化，设置插件管理器
        if hasattr(self, 'sidebar') and self.sidebar:
            self.sidebar.set_plugin_manager(plugin_manager)

    def get_plugin_manager(self):
        """获取插件管理器

        Returns:
            插件管理器实例
        """
        return self.plugin_manager


