#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtCore import QDir
from app.main_window import MainWindow
from manager.style_manager import StyleManager
from app.app_init import initialize_application

def main():
    app = initialize_application()

    # 加载浅色样式
    StyleManager.load_style("light_style")

    # Create and show the main window
    window = MainWindow()
    window.show()

    # Start the application event loop
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
