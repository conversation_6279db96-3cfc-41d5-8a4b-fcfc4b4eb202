#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import logging
from PySide6.QtCore import QDir
from app.main_window import MainWindow
from manager.style_manager import StyleManager
from app.app_init import (
    initialize_application,
    initialize_plugin_system_for_app,
    load_and_activate_plugins,
    shutdown_plugin_system
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tdpv/logs/tdpv.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """主函数"""
    plugin_manager = None

    try:
        # 初始化应用程序
        app = initialize_application()
        logger.info("应用程序初始化完成")

        # 加载浅色样式
        StyleManager.load_style("light_style")
        logger.info("样式加载完成")

        # 初始化插件系统
        plugin_manager = initialize_plugin_system_for_app()

        # 创建并显示主窗口
        window = MainWindow()

        # 将插件管理器传递给主窗口
        if plugin_manager:
            window.set_plugin_manager(plugin_manager)

        window.show()
        logger.info("主窗口已显示")

        # 加载并激活插件
        if plugin_manager:
            load_and_activate_plugins(plugin_manager)

        # 启动应用程序事件循环
        logger.info("启动应用程序事件循环")
        exit_code = app.exec()

        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code

    except Exception as e:
        logger.error(f"应用程序运行时出错: {e}")
        return 1

    finally:
        # 清理插件系统
        if plugin_manager:
            shutdown_plugin_system(plugin_manager)

if __name__ == "__main__":
    sys.exit(main())
