#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件管理器插件

该插件提供文件浏览和管理功能，包括：
- 文件树视图
- 最近文件列表
- 文件操作命令
- 文件事件处理
"""

import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTreeView, 
    QListWidget, QListWidgetItem, QPushButton, QFileSystemModel,
    QSplitter, QGroupBox
)
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QPixmap, QIcon


class FileManagerPlugin:
    """文件管理器插件类"""
    
    def __init__(self):
        self.recent_files = []
        self.context = None
        self.icon_widget = None
        self.panel_widget = None
    
    def activate(self, context):
        """激活插件"""
        self.context = context
        
        print("文件管理器插件已激活")
        
        # 创建UI组件
        self.icon_widget = self.create_icon_widget()
        self.panel_widget = self.create_panel_widget()
        
        # 注册侧边栏组件
        context.add_sidebar_icon("file_manager", self.icon_widget)
        context.add_sidebar_panel("file_manager", self.panel_widget)
        
        # 注册命令
        context.register_command("file_manager.open", self.open_file, "打开文件", "Ctrl+O")
        context.register_command("file_manager.recent", self.show_recent, "显示最近文件")
        context.register_command("file_manager.refresh", self.refresh_view, "刷新文件视图", "F5")
        
        # 订阅事件
        context.subscribe("file.opened", self.on_file_opened)
        context.subscribe("file.closed", self.on_file_closed)
        context.subscribe("folder.opened", self.on_folder_opened)
        
        # 发布插件激活事件
        context.publish("plugin.file_manager.activated", {
            'plugin_id': 'file_manager',
            'features': ['file_browser', 'recent_files', 'file_operations']
        })
    
    def create_icon_widget(self):
        """创建侧边栏图标"""
        icon_widget = QLabel()
        
        # 尝试加载图标
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "resources", "file_icon.png")
            if os.path.exists(icon_path):
                pixmap = QPixmap(icon_path)
                icon_widget.setPixmap(pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            else:
                # 使用文本作为图标
                icon_widget.setText("📁")
                icon_widget.setAlignment(Qt.AlignCenter)
        except Exception:
            # 备用文本图标
            icon_widget.setText("📁")
            icon_widget.setAlignment(Qt.AlignCenter)
        
        icon_widget.setToolTip("文件管理器")
        icon_widget.setFixedSize(32, 32)
        icon_widget.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
                font-size: 16px;
            }
            QLabel:hover {
                background-color: rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
        """)
        
        return icon_widget
    
    def create_panel_widget(self):
        """创建侧边栏面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("文件管理器")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 文件树组
        file_tree_group = QGroupBox("文件浏览器")
        file_tree_layout = QVBoxLayout(file_tree_group)
        
        # 文件树视图
        self.file_tree = QTreeView()
        self.file_model = QFileSystemModel()
        self.file_model.setRootPath(QDir.currentPath())
        self.file_tree.setModel(self.file_model)
        self.file_tree.setRootIndex(self.file_model.index(QDir.currentPath()))
        
        # 隐藏除名称外的其他列
        self.file_tree.setColumnHidden(1, True)  # Size
        self.file_tree.setColumnHidden(2, True)  # Type
        self.file_tree.setColumnHidden(3, True)  # Date Modified
        
        # 连接双击事件
        self.file_tree.doubleClicked.connect(self.on_file_tree_double_clicked)
        
        file_tree_layout.addWidget(self.file_tree)
        splitter.addWidget(file_tree_group)
        
        # 最近文件组
        recent_group = QGroupBox("最近文件")
        recent_layout = QVBoxLayout(recent_group)
        
        self.recent_list = QListWidget()
        self.recent_list.itemDoubleClicked.connect(self.on_recent_item_double_clicked)
        recent_layout.addWidget(self.recent_list)
        
        # 清空按钮
        clear_button = QPushButton("清空列表")
        clear_button.clicked.connect(self.clear_recent_files)
        recent_layout.addWidget(clear_button)
        
        splitter.addWidget(recent_group)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 2)  # 文件树占更多空间
        splitter.setStretchFactor(1, 1)  # 最近文件占较少空间
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        open_button = QPushButton("打开文件")
        open_button.clicked.connect(self.open_file)
        button_layout.addWidget(open_button)
        
        refresh_button = QPushButton("刷新")
        refresh_button.clicked.connect(self.refresh_view)
        button_layout.addWidget(refresh_button)
        
        layout.addLayout(button_layout)
        
        return panel
    
    def on_file_tree_double_clicked(self, index):
        """文件树双击事件处理"""
        file_path = self.file_model.filePath(index)
        if os.path.isfile(file_path):
            self.open_file_by_path(file_path)
        elif os.path.isdir(file_path):
            # 发布文件夹打开事件
            if self.context:
                self.context.publish("folder.opened", file_path)
    
    def on_recent_item_double_clicked(self, item):
        """最近文件列表双击事件处理"""
        file_path = item.text()
        if os.path.exists(file_path):
            self.open_file_by_path(file_path)
        else:
            # 文件不存在，从列表中移除
            self.recent_files.remove(file_path)
            self.update_recent_list()
    
    def open_file(self):
        """打开文件命令处理"""
        if self.context:
            file_path = self.context.show_file_dialog("选择要打开的文件")
            if file_path:
                self.open_file_by_path(file_path)
    
    def open_file_by_path(self, file_path):
        """通过路径打开文件"""
        if self.context:
            # 发布文件打开事件
            self.context.publish("file.opening", file_path)
            
            # 这里可以添加实际的文件打开逻辑
            print(f"打开文件: {file_path}")
            
            # 发布文件已打开事件
            self.context.publish("file.opened", file_path)
    
    def show_recent(self):
        """显示最近文件"""
        print("最近访问的文件:")
        for idx, file_path in enumerate(self.recent_files, 1):
            print(f"{idx}. {file_path}")
    
    def refresh_view(self):
        """刷新文件视图"""
        if hasattr(self, 'file_model'):
            # 刷新文件模型
            current_path = self.file_model.rootPath()
            self.file_model.setRootPath("")
            self.file_model.setRootPath(current_path)
            print("文件视图已刷新")
    
    def clear_recent_files(self):
        """清空最近文件列表"""
        self.recent_files.clear()
        self.update_recent_list()
        print("最近文件列表已清空")
    
    def on_file_opened(self, event_type, file_path):
        """文件打开事件处理"""
        if file_path and file_path not in self.recent_files:
            self.recent_files.insert(0, file_path)
            # 限制最近文件数量
            if len(self.recent_files) > 10:
                self.recent_files = self.recent_files[:10]
            self.update_recent_list()
            print(f"文件已添加到最近列表: {file_path}")
    
    def on_file_closed(self, event_type, file_path):
        """文件关闭事件处理"""
        print(f"文件已关闭: {file_path}")
    
    def on_folder_opened(self, event_type, folder_path):
        """文件夹打开事件处理"""
        if hasattr(self, 'file_tree') and hasattr(self, 'file_model'):
            # 设置文件树根目录
            index = self.file_model.index(folder_path)
            if index.isValid():
                self.file_tree.setRootIndex(index)
                print(f"文件树已切换到: {folder_path}")
    
    def update_recent_list(self):
        """更新最近文件列表显示"""
        if hasattr(self, 'recent_list'):
            self.recent_list.clear()
            for file_path in self.recent_files:
                item = QListWidgetItem(file_path)
                item.setToolTip(file_path)
                self.recent_list.addItem(item)
    
    def get_properties(self):
        """获取插件属性（用于属性面板显示）"""
        return {
            '插件信息': {
                '名称': {'type': 'string', 'value': '文件管理器'},
                '版本': {'type': 'string', 'value': '1.0.0'},
                '状态': {'type': 'string', 'value': '已激活'}
            },
            '统计信息': {
                '最近文件数量': {'type': 'int', 'value': len(self.recent_files)},
                '当前目录': {'type': 'string', 'value': getattr(self.file_model, 'rootPath', lambda: '')() if hasattr(self, 'file_model') else ''}
            }
        }
    
    def set_property(self, prop_name, value):
        """设置插件属性"""
        print(f"设置属性: {prop_name} = {value}")
    
    def deactivate(self):
        """停用插件"""
        print("文件管理器插件已停用")
        
        # 清理资源
        if self.context:
            self.context.cleanup()
        
        # 发布插件停用事件
        if self.context:
            self.context.publish("plugin.file_manager.deactivated", {
                'plugin_id': 'file_manager'
            })
