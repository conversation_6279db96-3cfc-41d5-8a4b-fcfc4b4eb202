#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件系统模块

该模块提供了插件系统的事件总线和事件类型定义。
"""

from .bus import (
    EventBus, EventPriority, EventSubscription, Event,
    get_event_bus, init_event_bus
)

from .types import (
    SystemEvents, FileEvents, UIEvents, CommandEvents, DataEvents,
    PluginEventData, FileEventData, UIEventData, CommandEventData, DataEventData,
    EventPatterns,
    create_plugin_event_data, create_file_event_data, create_ui_event_data,
    create_command_event_data, create_data_event_data,
    is_valid_event_type, get_event_description, get_all_event_types
)

__all__ = [
    # 事件总线
    'EventBus', 'EventPriority', 'EventSubscription', 'Event',
    'get_event_bus', 'init_event_bus',
    
    # 事件类型
    'SystemEvents', 'FileEvents', 'UIEvents', 'CommandEvents', 'DataEvents',
    
    # 事件数据
    'PluginEventData', 'FileEventData', 'UIEventData', 'CommandEventData', 'DataEventData',
    
    # 事件模式
    'EventPatterns',
    
    # 工具函数
    'create_plugin_event_data', 'create_file_event_data', 'create_ui_event_data',
    'create_command_event_data', 'create_data_event_data',
    'is_valid_event_type', 'get_event_description', 'get_all_event_types'
]
