#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QFont

class ActionButton(QPushButton):
    """欢迎界面上的操作按钮"""

    def __init__(self, icon_path, text, parent=None):
        super().__init__(parent)
        self.setText(text)
        self.setIcon(QIcon(icon_path))
        self.setIconSize(QSize(16, 16))
        self.setCursor(Qt.PointingHandCursor)
        self.setFlat(True)
        self.setStyleSheet("""
            QPushButton {
                text-align: left;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #E0E0E0;
            }
            QPushButton:pressed {
                background-color: #D0D0D0;
            }
        """)


class RecentItem(QFrame):
    """最近打开的项目条目"""

    clicked = Signal(str)  # 路径信号

    def __init__(self, name, path, parent=None):
        super().__init__(parent)
        self.path = path
        self.setup_ui(name)

    def setup_ui(self, name):
        self.setFrameShape(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)

        icon_label = QLabel()
        icon_label.setPixmap(QIcon("tdpv/icons/file.svg").pixmap(16, 16))

        name_label = QLabel(name)

        layout.addWidget(icon_label)
        layout.addWidget(name_label)
        layout.addStretch()

        self.setStyleSheet("""
            RecentItem {
                border: none;
                border-radius: 4px;
                background-color: transparent;
            }
            RecentItem:hover {
                background-color: #E0E0E0;
            }
        """)

    def mousePressEvent(self, event):
        super().mousePressEvent(event)
        self.clicked.emit(self.path)


class WelcomePage(QWidget):
    """欢迎界面组件"""

    open_file = Signal(str)
    open_folder = Signal(str)
    create_new_file = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 内容容器（居中显示）
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)

        # 添加水平和垂直间距，使内容居中
        main_layout.addStretch(1)
        main_layout.addWidget(content_widget, 0, Qt.AlignCenter)
        main_layout.addStretch(1)

        # 标题部分
        title_layout = QVBoxLayout()
        title_layout.setAlignment(Qt.AlignCenter)

        # 应用图标
        icon_label = QLabel()
        icon_label.setPixmap(QIcon("tdpv/icons/app.svg").pixmap(64, 64))
        icon_label.setAlignment(Qt.AlignCenter)

        # 应用标题
        title_label = QLabel("TDPV")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)

        # 添加到标题布局
        title_layout.addWidget(icon_label)
        title_layout.addWidget(title_label)

        # 添加到内容布局
        content_layout.addLayout(title_layout)

        # 创建两列布局
        columns_layout = QHBoxLayout()
        columns_layout.setSpacing(40)

        # 左侧列 - 快速操作
        left_column = QVBoxLayout()
        left_column.setSpacing(10)

        # 快速操作标题
        start_label = QLabel("开始")
        start_font = QFont()
        start_font.setPointSize(12)
        start_font.setBold(True)
        start_label.setFont(start_font)

        # 快速操作按钮
        new_file_btn = ActionButton("tdpv/icons/file.svg", "新建文件...")
        open_file_btn = ActionButton("tdpv/icons/file.svg", "打开文件...")
        open_folder_btn = ActionButton("tdpv/icons/file.svg", "打开文件夹...")

        # 连接信号
        new_file_btn.clicked.connect(self.create_new_file.emit)
        open_file_btn.clicked.connect(lambda: self.open_file.emit(""))
        open_folder_btn.clicked.connect(lambda: self.open_folder.emit(""))

        # 添加到左侧列
        left_column.addWidget(start_label)
        left_column.addWidget(new_file_btn)
        left_column.addWidget(open_file_btn)
        left_column.addWidget(open_folder_btn)
        left_column.addStretch()

        # 右侧列 - 最近打开
        right_column = QVBoxLayout()
        right_column.setSpacing(10)

        # 最近打开标题
        recent_label = QLabel("最近打开")
        recent_label.setFont(start_font)  # 使用与"开始"相同的字体

        # 添加到右侧列
        right_column.addWidget(recent_label)

        # 添加一些示例最近项目
        # 实际应用中，这些应该从配置文件或设置中加载
        recent_items = [
            ("示例文件1.txt", "/path/to/file1.txt"),
            ("示例项目1", "/path/to/project1"),
            ("示例文件2.txt", "/path/to/file2.txt"),
        ]

        for name, path in recent_items:
            item = RecentItem(name, path)
            item.clicked.connect(lambda p=path: self.open_file.emit(p))
            right_column.addWidget(item)

        right_column.addStretch()

        # 添加两列到布局
        columns_layout.addLayout(left_column)
        columns_layout.addLayout(right_column)

        # 添加到内容布局
        content_layout.addLayout(columns_layout)

        # 底部链接部分
        links_layout = QHBoxLayout()
        links_layout.setAlignment(Qt.AlignCenter)
        links_layout.setSpacing(20)

        # 添加一些链接
        help_btn = QPushButton("帮助")
        help_btn.setFlat(True)
        help_btn.setCursor(Qt.PointingHandCursor)

        about_btn = QPushButton("关于")
        about_btn.setFlat(True)
        about_btn.setCursor(Qt.PointingHandCursor)

        # 添加到链接布局
        links_layout.addWidget(help_btn)
        links_layout.addWidget(about_btn)

        # 添加到内容布局
        content_layout.addLayout(links_layout)

        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: #F5F5F5;
                color: #333333;
            }
            QPushButton {
                border: none;
                padding: 5px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #E0E0E0;
            }
        """)
