#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
事件总线模块

该模块实现了插件系统的事件总线，提供事件发布、订阅和管理功能。
支持同步/异步事件分发、通配符订阅、事件优先级等特性。
"""

import asyncio
import fnmatch
import logging
import threading
import uuid
from typing import Any, Callable, Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """事件优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class EventSubscription:
    """事件订阅信息"""
    handler_id: str
    event_pattern: str
    handler: Callable
    priority: EventPriority
    is_async: bool = False
    is_once: bool = False  # 是否只执行一次


@dataclass
class Event:
    """事件对象"""
    event_type: str
    data: Any
    timestamp: float
    source: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL


class EventBus:
    """事件总线
    
    全系统通信中枢，支持：
    - 同步/异步混合事件分发
    - 多级事件优先级管理
    - 通配符订阅（如 file.*）
    - 事件过滤和路由机制
    """
    
    def __init__(self, max_workers: int = 4):
        """初始化事件总线
        
        Args:
            max_workers: 异步事件处理的最大工作线程数
        """
        self._subscriptions: Dict[str, List[EventSubscription]] = {}
        self._wildcard_subscriptions: List[EventSubscription] = []
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._event_history: List[Event] = []
        self._max_history_size = 1000
        
        # 统计信息
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'subscription_count': 0
        }
    
    def subscribe(self, event_pattern: str, handler: Callable, 
                 priority: EventPriority = EventPriority.NORMAL,
                 is_async: bool = False, is_once: bool = False) -> str:
        """订阅事件
        
        Args:
            event_pattern: 事件模式，支持通配符（如 file.*, *.opened）
            handler: 事件处理函数
            priority: 事件优先级
            is_async: 是否异步处理
            is_once: 是否只执行一次
            
        Returns:
            str: 订阅ID，用于取消订阅
        """
        handler_id = str(uuid.uuid4())
        subscription = EventSubscription(
            handler_id=handler_id,
            event_pattern=event_pattern,
            handler=handler,
            priority=priority,
            is_async=is_async,
            is_once=is_once
        )
        
        with self._lock:
            # 检查是否为通配符模式
            if '*' in event_pattern:
                self._wildcard_subscriptions.append(subscription)
                # 按优先级排序
                self._wildcard_subscriptions.sort(
                    key=lambda x: x.priority.value, reverse=True
                )
            else:
                # 精确匹配订阅
                if event_pattern not in self._subscriptions:
                    self._subscriptions[event_pattern] = []
                self._subscriptions[event_pattern].append(subscription)
                # 按优先级排序
                self._subscriptions[event_pattern].sort(
                    key=lambda x: x.priority.value, reverse=True
                )
            
            self._stats['subscription_count'] += 1
        
        logger.debug(f"订阅事件: {event_pattern}, 处理器ID: {handler_id}")
        return handler_id
    
    def unsubscribe(self, handler_id: str) -> bool:
        """取消订阅
        
        Args:
            handler_id: 订阅ID
            
        Returns:
            bool: 是否成功取消订阅
        """
        with self._lock:
            # 在精确匹配订阅中查找
            for event_type, subscriptions in self._subscriptions.items():
                for i, sub in enumerate(subscriptions):
                    if sub.handler_id == handler_id:
                        subscriptions.pop(i)
                        if not subscriptions:
                            del self._subscriptions[event_type]
                        self._stats['subscription_count'] -= 1
                        logger.debug(f"取消订阅: {handler_id}")
                        return True
            
            # 在通配符订阅中查找
            for i, sub in enumerate(self._wildcard_subscriptions):
                if sub.handler_id == handler_id:
                    self._wildcard_subscriptions.pop(i)
                    self._stats['subscription_count'] -= 1
                    logger.debug(f"取消通配符订阅: {handler_id}")
                    return True
        
        logger.warning(f"未找到订阅ID: {handler_id}")
        return False
    
    def publish(self, event_type: str, data: Any = None, 
               source: Optional[str] = None,
               priority: EventPriority = EventPriority.NORMAL) -> None:
        """发布同步事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            priority: 事件优先级
        """
        import time
        
        event = Event(
            event_type=event_type,
            data=data,
            timestamp=time.time(),
            source=source,
            priority=priority
        )
        
        self._add_to_history(event)
        self._stats['events_published'] += 1
        
        # 获取匹配的订阅
        matching_subscriptions = self._get_matching_subscriptions(event_type)
        
        # 按优先级处理事件
        for subscription in matching_subscriptions:
            try:
                if subscription.is_async:
                    # 异步处理
                    self._executor.submit(self._handle_event_async, subscription, event)
                else:
                    # 同步处理
                    self._handle_event_sync(subscription, event)
                
                # 如果是一次性订阅，处理后移除
                if subscription.is_once:
                    self.unsubscribe(subscription.handler_id)
                    
            except Exception as e:
                logger.error(f"处理事件时出错: {event_type}, 错误: {e}")
        
        logger.debug(f"发布事件: {event_type}, 处理器数量: {len(matching_subscriptions)}")
    
    def publish_async(self, event_type: str, data: Any = None,
                     source: Optional[str] = None,
                     priority: EventPriority = EventPriority.NORMAL) -> None:
        """发布异步事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            priority: 事件优先级
        """
        # 在线程池中异步发布
        self._executor.submit(self.publish, event_type, data, source, priority)
    
    def _get_matching_subscriptions(self, event_type: str) -> List[EventSubscription]:
        """获取匹配的订阅列表
        
        Args:
            event_type: 事件类型
            
        Returns:
            List[EventSubscription]: 匹配的订阅列表（按优先级排序）
        """
        matching = []
        
        with self._lock:
            # 精确匹配
            if event_type in self._subscriptions:
                matching.extend(self._subscriptions[event_type])
            
            # 通配符匹配
            for subscription in self._wildcard_subscriptions:
                if fnmatch.fnmatch(event_type, subscription.event_pattern):
                    matching.append(subscription)
        
        # 按优先级排序
        matching.sort(key=lambda x: x.priority.value, reverse=True)
        return matching
    
    def _handle_event_sync(self, subscription: EventSubscription, event: Event) -> None:
        """同步处理事件
        
        Args:
            subscription: 订阅信息
            event: 事件对象
        """
        try:
            subscription.handler(event.event_type, event.data)
            self._stats['events_processed'] += 1
        except Exception as e:
            logger.error(f"同步事件处理器出错: {subscription.handler_id}, 错误: {e}")
    
    def _handle_event_async(self, subscription: EventSubscription, event: Event) -> None:
        """异步处理事件
        
        Args:
            subscription: 订阅信息
            event: 事件对象
        """
        try:
            subscription.handler(event.event_type, event.data)
            self._stats['events_processed'] += 1
        except Exception as e:
            logger.error(f"异步事件处理器出错: {subscription.handler_id}, 错误: {e}")
    
    def _add_to_history(self, event: Event) -> None:
        """添加事件到历史记录
        
        Args:
            event: 事件对象
        """
        with self._lock:
            self._event_history.append(event)
            # 限制历史记录大小
            if len(self._event_history) > self._max_history_size:
                self._event_history.pop(0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return {
                **self._stats,
                'active_subscriptions': len(self._subscriptions) + len(self._wildcard_subscriptions),
                'history_size': len(self._event_history)
            }
    
    def get_subscriptions(self) -> Dict[str, List[str]]:
        """获取所有订阅信息
        
        Returns:
            Dict[str, List[str]]: 事件类型到处理器ID列表的映射
        """
        result = {}
        
        with self._lock:
            # 精确匹配订阅
            for event_type, subscriptions in self._subscriptions.items():
                result[event_type] = [sub.handler_id for sub in subscriptions]
            
            # 通配符订阅
            wildcard_handlers = [sub.handler_id for sub in self._wildcard_subscriptions]
            if wildcard_handlers:
                result['*'] = wildcard_handlers
        
        return result
    
    def clear_history(self) -> None:
        """清空事件历史记录"""
        with self._lock:
            self._event_history.clear()
    
    def shutdown(self) -> None:
        """关闭事件总线"""
        logger.info("正在关闭事件总线...")
        self._executor.shutdown(wait=True)
        with self._lock:
            self._subscriptions.clear()
            self._wildcard_subscriptions.clear()
            self._event_history.clear()
        logger.info("事件总线已关闭")


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线实例
    
    Returns:
        EventBus: 全局事件总线实例
    """
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


def init_event_bus(max_workers: int = 4) -> EventBus:
    """初始化全局事件总线
    
    Args:
        max_workers: 最大工作线程数
        
    Returns:
        EventBus: 事件总线实例
    """
    global _global_event_bus
    if _global_event_bus is not None:
        _global_event_bus.shutdown()
    _global_event_bus = EventBus(max_workers)
    return _global_event_bus
