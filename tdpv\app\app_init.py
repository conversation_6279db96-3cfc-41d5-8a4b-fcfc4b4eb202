#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用程序初始化模块

该模块提供应用程序初始化相关的功能，包括创建QApplication实例、设置应用程序属性和初始化插件系统。
"""

import sys
import logging
from PySide6.QtWidgets import QApplication

# 导入插件系统
from tdpv.plugin import initialize_plugin_system, get_event_bus, SystemEvents

logger = logging.getLogger(__name__)


def initialize_application():
    """初始化应用程序

    创建QApplication实例并设置应用程序名称和组织名称。

    Returns:
        QApplication: 初始化后的应用程序实例
    """
    app = QApplication(sys.argv)

    # Set application name and organization
    app.setApplicationName("TDPV")
    app.setOrganizationName("TDPV")

    return app


def initialize_plugin_system_for_app():
    """为应用程序初始化插件系统

    Returns:
        PluginManager: 插件管理器实例
    """
    try:
        # 初始化插件系统
        plugin_manager = initialize_plugin_system(
            plugin_dirs=['tdpv/plugins'],
            enable_hot_reload=True
        )

        # 获取事件总线
        event_bus = get_event_bus()

        # 发布应用启动事件
        event_bus.publish(SystemEvents.APP_STARTING, {
            'app_name': 'TDPV',
            'version': '1.0.0'
        })

        logger.info("插件系统初始化成功")
        return plugin_manager

    except Exception as e:
        logger.error(f"插件系统初始化失败: {e}")
        return None


def load_and_activate_plugins(plugin_manager):
    """加载并激活插件

    Args:
        plugin_manager: 插件管理器实例

    Returns:
        tuple: (成功加载数量, 成功激活数量)
    """
    if not plugin_manager:
        logger.warning("插件管理器未初始化，跳过插件加载")
        return 0, 0

    try:
        # 加载所有插件
        load_success, load_failure = plugin_manager.load_all_plugins()
        logger.info(f"插件加载完成: 成功 {load_success}, 失败 {load_failure}")

        # 激活所有插件
        activate_success, activate_failure = plugin_manager.activate_all_plugins()
        logger.info(f"插件激活完成: 成功 {activate_success}, 失败 {activate_failure}")

        # 发布应用启动完成事件
        event_bus = get_event_bus()
        event_bus.publish(SystemEvents.APP_STARTED, {
            'plugins_loaded': load_success,
            'plugins_activated': activate_success
        })

        return load_success, activate_success

    except Exception as e:
        logger.error(f"插件加载激活失败: {e}")
        return 0, 0


def shutdown_plugin_system(plugin_manager):
    """关闭插件系统

    Args:
        plugin_manager: 插件管理器实例
    """
    if not plugin_manager:
        return

    try:
        # 发布应用停止事件
        event_bus = get_event_bus()
        event_bus.publish(SystemEvents.APP_STOPPING, {
            'reason': 'normal_shutdown'
        })

        # 关闭插件管理器
        plugin_manager.shutdown()

        # 发布应用已停止事件
        event_bus.publish(SystemEvents.APP_STOPPED, {})

        # 关闭事件总线
        event_bus.shutdown()

        logger.info("插件系统已关闭")

    except Exception as e:
        logger.error(f"关闭插件系统失败: {e}")
