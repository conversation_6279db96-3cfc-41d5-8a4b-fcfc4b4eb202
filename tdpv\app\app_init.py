#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
应用程序初始化模块

该模块提供应用程序初始化相关的功能，包括创建QApplication实例和设置应用程序属性。
"""

import sys
from PySide6.QtWidgets import QApplication


def initialize_application():
    """初始化应用程序

    创建QApplication实例并设置应用程序名称和组织名称。

    Returns:
        QApplication: 初始化后的应用程序实例
    """
    app = QApplication(sys.argv)

    # Set application name and organization
    app.setApplicationName("TDPV")
    app.setOrganizationName("TDPV")

    return app
