#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单插件测试

测试插件是否能正确加载和激活
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tdpv'))

def test_plugin_loading():
    """测试插件加载"""
    print("🧪 测试插件加载...")
    
    try:
        # 导入插件系统
        from tdpv.plugin import PluginManager
        
        # 创建插件管理器
        manager = PluginManager(['tdpv/plugins'])
        print("✅ 插件管理器创建成功")
        
        # 发现插件
        discovered = manager.discover_plugins()
        print(f"✅ 发现插件: {discovered}")
        
        if not discovered:
            print("❌ 没有发现任何插件")
            return False
        
        # 加载插件
        load_success, load_failure = manager.load_all_plugins()
        print(f"✅ 加载结果: 成功 {load_success}, 失败 {load_failure}")
        
        # 显示插件状态
        for plugin_id in discovered:
            status = manager.get_plugin_status(plugin_id)
            if status:
                print(f"📦 {plugin_id}: {status['state']}")
                if status.get('error_message'):
                    print(f"   错误: {status['error_message']}")
        
        return load_success > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plugin_activation():
    """测试插件激活"""
    print("\n🧪 测试插件激活...")
    
    try:
        # 需要QApplication来创建UI组件
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from tdpv.plugin import PluginManager
        
        # 创建插件管理器
        manager = PluginManager(['tdpv/plugins'])
        
        # 发现和加载插件
        discovered = manager.discover_plugins()
        load_success, load_failure = manager.load_all_plugins()
        
        if load_success == 0:
            print("❌ 没有成功加载的插件")
            return False
        
        # 激活插件
        activate_success, activate_failure = manager.activate_all_plugins()
        print(f"✅ 激活结果: 成功 {activate_success}, 失败 {activate_failure}")
        
        # 显示激活后的状态
        for plugin_id in discovered:
            status = manager.get_plugin_status(plugin_id)
            if status:
                print(f"📦 {plugin_id}: {status['state']}")
                if status.get('error_message'):
                    print(f"   错误: {status['error_message']}")
        
        # 清理
        manager.shutdown()
        
        return activate_success > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简单插件测试")
    print("=" * 40)
    
    # 测试1: 插件加载
    test1_result = test_plugin_loading()
    
    # 测试2: 插件激活
    test2_result = test_plugin_activation()
    
    print("\n📊 测试结果:")
    print(f"插件加载: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"插件激活: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！")
        return 0
    else:
        print("\n⚠️ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
