# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.python-version
.pytest_cache/
.mypy_cache/
.pytype/
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
Pipfile.lock
__pypackages__/

# 项目特定
/logs/
/config/
/build/
/dist/
/.idea/
/.vscode/
/.DS_Store
Thumbs.db

# 测试相关
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 文档相关
docs/_build/
/site

# 其他
*.log
local_settings.py
db.sqlite3
instance/
.webassets-cache
.scrapy
target/
.ipynb_checkpoints
profile_default/
ipython_config.py
celerybeat-schedule
celerybeat.pid
*.sage.py
.spyderproject
.spyproject
.ropeproject

# Distribution / packaging
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pyenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# Cython debug symbols
cython_debug/

# Operating System Files
.DS_Store
Thumbs.db

# Office files
*.ppt
*.pptx

# IDE
.vscode/
.idea/

# 样式表缓存
*.qssc